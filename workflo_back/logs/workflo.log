INFO 2025-05-31 21:27:45,128 autoreload 41513 128464055791616 Watching for file changes with StatReloader
INFO 2025-05-31 21:27:55,334 autoreload 41513 128464055791616 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/overtime_views.py changed, reloading.
INFO 2025-05-31 21:27:56,596 autoreload 41602 123914895634432 Watching for file changes with StatReloader
INFO 2025-05-31 21:28:43,811 autoreload 41602 123914895634432 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/overtime_views.py changed, reloading.
INFO 2025-05-31 21:28:45,142 autoreload 42074 139002434973696 Watching for file changes with StatReloader
INFO 2025-05-31 21:30:15,528 autoreload 42903 *************** Watching for file changes with StatReloader
INFO 2025-05-31 21:30:43,467 basehttp 42903 *************** "GET /api/docs/ HTTP/1.1" 200 4714
ERROR 2025-05-31 21:30:48,548 log 42903 *************** Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 104, in get_operation
    auth = self.get_auth()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 364, in get_auth
    perms = [p.__class__ for p in self.view.get_permissions()]
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/payroll_views.py", line 23, in get_permissions
    if hasattr(self.request, 'user') and self.request.user.role not in ['admin', 'hr', 'accountant']:
AttributeError: 'AnonymousUser' object has no attribute 'role'
ERROR 2025-05-31 21:30:48,551 basehttp 42903 *************** "GET /api/schema/ HTTP/1.1" 500 140498
ERROR 2025-05-31 21:30:48,962 log 42903 *************** Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 104, in get_operation
    auth = self.get_auth()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 364, in get_auth
    perms = [p.__class__ for p in self.view.get_permissions()]
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/payroll_views.py", line 23, in get_permissions
    if hasattr(self.request, 'user') and self.request.user.role not in ['admin', 'hr', 'accountant']:
AttributeError: 'AnonymousUser' object has no attribute 'role'
ERROR 2025-05-31 21:30:48,963 basehttp 42903 *************** "GET /api/schema/ HTTP/1.1" 500 140498
WARNING 2025-05-31 21:30:50,820 log 42903 *************** Unauthorized: /api/system/health/
WARNING 2025-05-31 21:30:50,820 basehttp 42903 *************** "GET /api/system/health/ HTTP/1.1" 401 172
INFO 2025-05-31 21:30:59,771 basehttp 42903 *************** "POST /auth/login/ HTTP/1.1" 200 796
INFO 2025-05-31 21:31:09,266 monitoring 42903 *************** Health check <NAME_EMAIL>, status: warning
INFO 2025-05-31 21:31:09,267 basehttp 42903 *************** "GET /api/system/health/ HTTP/1.1" 200 975
INFO 2025-05-31 21:31:15,131 basehttp 42903 *************** "GET /api/docs/ HTTP/1.1" 200 4714
ERROR 2025-05-31 21:31:16,107 log 42903 *************** Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 104, in get_operation
    auth = self.get_auth()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 364, in get_auth
    perms = [p.__class__ for p in self.view.get_permissions()]
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/payroll_views.py", line 23, in get_permissions
    if hasattr(self.request, 'user') and self.request.user.role not in ['admin', 'hr', 'accountant']:
AttributeError: 'AnonymousUser' object has no attribute 'role'
ERROR 2025-05-31 21:31:16,109 basehttp 42903 *************** "GET /api/schema/ HTTP/1.1" 500 140498
ERROR 2025-05-31 21:31:16,676 log 42903 *************** Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 104, in get_operation
    auth = self.get_auth()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 364, in get_auth
    perms = [p.__class__ for p in self.view.get_permissions()]
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/payroll_views.py", line 23, in get_permissions
    if hasattr(self.request, 'user') and self.request.user.role not in ['admin', 'hr', 'accountant']:
AttributeError: 'AnonymousUser' object has no attribute 'role'
ERROR 2025-05-31 21:31:16,677 basehttp 42903 *************** "GET /api/schema/ HTTP/1.1" 500 140498
INFO 2025-05-31 21:32:39,956 autoreload 42903 *************** /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/payroll_views.py changed, reloading.
INFO 2025-05-31 21:32:41,672 autoreload 44043 *************** Watching for file changes with StatReloader
INFO 2025-05-31 21:32:53,949 autoreload 44043 *************** /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/payroll_views.py changed, reloading.
INFO 2025-05-31 21:32:55,284 autoreload 44147 *************** Watching for file changes with StatReloader
INFO 2025-05-31 21:33:01,978 basehttp 44147 *************** "GET /api/docs/ HTTP/1.1" 200 4714
ERROR 2025-05-31 21:33:03,670 log 44147 *************** Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 104, in get_operation
    auth = self.get_auth()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 364, in get_auth
    perms = [p.__class__ for p in self.view.get_permissions()]
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/employee_views.py", line 219, in get_permissions
    if hasattr(self.request, 'user') and self.request.user.role not in ['admin', 'hr', 'accountant']:
AttributeError: 'AnonymousUser' object has no attribute 'role'
ERROR 2025-05-31 21:33:03,674 basehttp 44147 *************** "GET /api/schema/ HTTP/1.1" 500 142104
ERROR 2025-05-31 21:33:04,959 log 44147 *************** Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 104, in get_operation
    auth = self.get_auth()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 364, in get_auth
    perms = [p.__class__ for p in self.view.get_permissions()]
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/employee_views.py", line 219, in get_permissions
    if hasattr(self.request, 'user') and self.request.user.role not in ['admin', 'hr', 'accountant']:
AttributeError: 'AnonymousUser' object has no attribute 'role'
ERROR 2025-05-31 21:33:04,962 basehttp 44147 *************** "GET /api/schema/ HTTP/1.1" 500 142104
INFO 2025-05-31 21:33:19,728 autoreload 44147 *************** /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/employee_views.py changed, reloading.
INFO 2025-05-31 21:33:20,847 autoreload 44486 *************** Watching for file changes with StatReloader
INFO 2025-05-31 21:33:32,587 autoreload 44486 *************** /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/employee_views.py changed, reloading.
INFO 2025-05-31 21:33:33,926 autoreload 44624 *************** Watching for file changes with StatReloader
INFO 2025-05-31 21:33:49,476 autoreload 44624 *************** /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/employee_views.py changed, reloading.
INFO 2025-05-31 21:33:50,720 autoreload 44792 *************** Watching for file changes with StatReloader
INFO 2025-05-31 21:33:54,535 basehttp 44792 134732613416512 "GET /api/docs/ HTTP/1.1" 200 4714
ERROR 2025-05-31 21:33:56,392 log 44792 134732613416512 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/utils.py", line 451, in get_operation
    return super().get_operation(path, path_regex, path_prefix, method, registry)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 92, in get_operation
    parameters = self._get_parameters()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 264, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 555, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/contrib/django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/rest_framework/backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: difficulty_level, category, is_mandatory, is_active
ERROR 2025-05-31 21:33:56,398 basehttp 44792 134732613416512 "GET /api/schema/ HTTP/1.1" 500 165892
ERROR 2025-05-31 21:33:56,946 log 44792 134732613416512 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/utils.py", line 451, in get_operation
    return super().get_operation(path, path_regex, path_prefix, method, registry)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 92, in get_operation
    parameters = self._get_parameters()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 264, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 555, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/contrib/django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/rest_framework/backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: difficulty_level, category, is_mandatory, is_active
ERROR 2025-05-31 21:33:56,947 basehttp 44792 134732613416512 "GET /api/schema/ HTTP/1.1" 500 165892
INFO 2025-05-31 21:34:11,770 autoreload 44792 *************** /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/attendance_views.py changed, reloading.
INFO 2025-05-31 21:34:13,139 autoreload 45022 138758716428288 Watching for file changes with StatReloader
INFO 2025-05-31 21:34:24,642 basehttp 45022 138758552811072 "GET /api/docs/ HTTP/1.1" 200 4714
ERROR 2025-05-31 21:34:26,073 log 45022 138758552811072 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/utils.py", line 451, in get_operation
    return super().get_operation(path, path_regex, path_prefix, method, registry)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 92, in get_operation
    parameters = self._get_parameters()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 264, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 555, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/contrib/django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/rest_framework/backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: difficulty_level, category, is_mandatory, is_active
ERROR 2025-05-31 21:34:26,078 basehttp 45022 138758552811072 "GET /api/schema/ HTTP/1.1" 500 165892
ERROR 2025-05-31 21:34:26,645 log 45022 138758552811072 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/utils.py", line 451, in get_operation
    return super().get_operation(path, path_regex, path_prefix, method, registry)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 92, in get_operation
    parameters = self._get_parameters()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 264, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 555, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/contrib/django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/rest_framework/backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: difficulty_level, category, is_mandatory, is_active
ERROR 2025-05-31 21:34:26,646 basehttp 45022 138758552811072 "GET /api/schema/ HTTP/1.1" 500 165892
INFO 2025-05-31 21:34:35,546 autoreload 45022 138758716428288 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/leave_views.py changed, reloading.
INFO 2025-05-31 21:34:36,460 autoreload 45331 128879623135232 Watching for file changes with StatReloader
INFO 2025-05-31 21:34:50,831 autoreload 45331 128879623135232 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/views/leave_views.py changed, reloading.
INFO 2025-05-31 21:34:51,962 autoreload 45448 138359475253248 Watching for file changes with StatReloader
ERROR 2025-05-31 21:35:05,340 log 45448 138359312741952 Internal Server Error: /api/schema/
Traceback (most recent call last):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/decorators/csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django/views/generic/base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 480, in raise_uncaught_exception
    raise exc
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/rest_framework/views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 84, in get
    return self._get_schema_response(request)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/views.py", line 92, in _get_schema_response
    data=generator.get_schema(request=request, public=self.serve_public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 285, in get_schema
    paths=self.parse(request, public),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/generators.py", line 256, in parse
    operation = view.schema.get_operation(
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/utils.py", line 451, in get_operation
    return super().get_operation(path, path_regex, path_prefix, method, registry)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 92, in get_operation
    parameters = self._get_parameters()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 264, in _get_parameters
    **dict_helper(self._get_filter_parameters()),
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/openapi.py", line 555, in _get_filter_parameters
    parameters += filter_extension.get_schema_operation_parameters(self)
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/drf_spectacular/contrib/django_filters.py", line 58, in get_schema_operation_parameters
    filterset_class = self.target.get_filterset_class(auto_schema.view, get_manager(model).none())
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/rest_framework/backends.py", line 49, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/filterset.py", line 82, in __new__
    new_class.base_filters = new_class.get_filters()
  File "/home/<USER>/Documents/augment-projects/naya/workflo_back/venv/lib/python3.10/site-packages/django_filters/filterset.py", line 371, in get_filters
    raise TypeError(
TypeError: 'Meta.fields' must not contain non-model field names: difficulty_level, category, is_mandatory, is_active
ERROR 2025-05-31 21:35:05,350 basehttp 45448 138359312741952 "GET /api/schema/ HTTP/1.1" 500 163447
INFO 2025-05-31 21:35:30,406 autoreload 45448 138359475253248 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/models/training.py changed, reloading.
INFO 2025-05-31 21:35:31,982 autoreload 45816 130375624634368 Watching for file changes with StatReloader
INFO 2025-05-31 21:35:47,366 autoreload 45816 130375624634368 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/models/training.py changed, reloading.
INFO 2025-05-31 21:35:48,527 autoreload 45979 138713510801408 Watching for file changes with StatReloader
INFO 2025-05-31 21:36:22,312 basehttp 45979 138713349748288 "GET /api/schema/ HTTP/1.1" 200 223919
INFO 2025-05-31 21:36:22,314 basehttp 45979 138713349748288 - Broken pipe from ('127.0.0.1', 60266)
INFO 2025-05-31 21:36:30,830 basehttp 45979 138713349748288 "GET /api/docs/ HTTP/1.1" 200 4714
INFO 2025-05-31 21:36:32,565 basehttp 45979 138713349748288 "GET /api/schema/ HTTP/1.1" 200 451533
INFO 2025-05-31 21:36:42,625 monitoring 45979 138713322477120 Health check <NAME_EMAIL>, status: warning
INFO 2025-05-31 21:36:42,626 basehttp 45979 138713322477120 "GET /api/system/health/ HTTP/1.1" 200 976
INFO 2025-05-31 21:41:55,879 autoreload 45979 138713510801408 /home/<USER>/Documents/augment-projects/naya/workflo_back/workflo_back/settings.py changed, reloading.
INFO 2025-05-31 21:41:57,471 autoreload 48368 125651307749376 Watching for file changes with StatReloader
INFO 2025-05-31 21:42:07,565 autoreload 48368 125651307749376 /home/<USER>/Documents/augment-projects/naya/workflo_back/workflo_back/settings.py changed, reloading.
INFO 2025-05-31 21:42:08,597 autoreload 48460 135473338560512 Watching for file changes with StatReloader
INFO 2025-05-31 21:42:22,129 autoreload 48460 135473338560512 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/serializers/employee_serializers.py changed, reloading.
INFO 2025-05-31 21:42:23,061 autoreload 48568 128936243519488 Watching for file changes with StatReloader
INFO 2025-05-31 21:43:02,159 autoreload 48568 128936243519488 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/serializers/employee_serializers.py changed, reloading.
INFO 2025-05-31 21:43:03,227 autoreload 48789 125093109301248 Watching for file changes with StatReloader
INFO 2025-05-31 21:43:20,563 autoreload 48789 125093109301248 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/serializers/employee_serializers.py changed, reloading.
INFO 2025-05-31 21:43:21,608 autoreload 48908 139553977057280 Watching for file changes with StatReloader
INFO 2025-05-31 21:46:21,801 autoreload 50362 138772545183744 Watching for file changes with StatReloader
INFO 2025-05-31 21:47:48,305 autoreload 48908 139553977057280 /home/<USER>/Documents/augment-projects/naya/workflo_back/core/serializers/employee_serializers.py changed, reloading.
INFO 2025-05-31 21:48:06,543 autoreload 51346 129892096937984 Watching for file changes with StatReloader
INFO 2025-06-01 11:00:23,944 autoreload 15744 128073941422080 Watching for file changes with StatReloader
INFO 2025-06-01 11:02:22,491 autoreload 17234 129444993912832 Watching for file changes with StatReloader
INFO 2025-06-01 11:29:02,958 test_database 34776 136801465577472 🚀 Starting comprehensive database tests...
INFO 2025-06-01 11:29:02,998 test_database 34776 136801465577472 
📋 Running: Database Connection
INFO 2025-06-01 11:29:02,998 test_database 34776 136801465577472 🔍 Testing database connection...
ERROR 2025-06-01 11:29:03,020 test_database 34776 136801465577472 ❌ Database connection failed: no such function: version
ERROR 2025-06-01 11:29:03,020 test_database 34776 136801465577472 ❌ Database Connection - FAILED
INFO 2025-06-01 11:29:03,020 test_database 34776 136801465577472 
📋 Running: Database Migrations
INFO 2025-06-01 11:29:03,020 test_database 34776 136801465577472 🔄 Testing database migrations...
INFO 2025-06-01 11:29:03,716 test_database 34776 136801465577472 📝 Making migrations...
INFO 2025-06-01 11:29:03,897 test_database 34776 136801465577472 ⚡ Applying migrations...
INFO 2025-06-01 11:29:04,292 test_database 34776 136801465577472 ✅ Migrations completed successfully
INFO 2025-06-01 11:29:04,293 test_database 34776 136801465577472 ✅ Database Migrations - PASSED
INFO 2025-06-01 11:29:04,293 test_database 34776 136801465577472 
📋 Running: Model Operations
INFO 2025-06-01 11:29:04,293 test_database 34776 136801465577472 🧪 Testing model operations...
INFO 2025-06-01 11:29:04,294 test_database 34776 136801465577472 👥 Users in database: 5
INFO 2025-06-01 11:29:06,650 test_database 34776 136801465577472 ✅ Test user created successfully
INFO 2025-06-01 11:29:06,674 test_database 34776 136801465577472 🏢 Departments: 5
INFO 2025-06-01 11:29:06,675 test_database 34776 136801465577472 🏖️ Leave types: 5
INFO 2025-06-01 11:29:06,675 test_database 34776 136801465577472 ✅ Model operations test passed
INFO 2025-06-01 11:29:06,675 test_database 34776 136801465577472 ✅ Model Operations - PASSED
INFO 2025-06-01 11:29:06,676 test_database 34776 136801465577472 
📋 Running: Database Performance
INFO 2025-06-01 11:29:06,676 test_database 34776 136801465577472 ⚡ Testing database performance...
ERROR 2025-06-01 11:29:06,676 test_database 34776 136801465577472 ❌ Performance test failed: no such table: auth_user
ERROR 2025-06-01 11:29:06,677 test_database 34776 136801465577472 ❌ Database Performance - FAILED
INFO 2025-06-01 11:29:06,677 test_database 34776 136801465577472 
📋 Running: Database Schema
INFO 2025-06-01 11:29:06,677 test_database 34776 136801465577472 🏗️ Testing database schema...
ERROR 2025-06-01 11:29:06,680 test_database 34776 136801465577472 ❌ Schema test failed: no such table: information_schema.tables
ERROR 2025-06-01 11:29:06,682 test_database 34776 136801465577472 ❌ Database Schema - FAILED
INFO 2025-06-01 11:29:06,684 test_database 34776 136801465577472 
📊 Test Results Summary:
INFO 2025-06-01 11:29:06,684 test_database 34776 136801465577472 ✅ Passed: 2/5
INFO 2025-06-01 11:29:06,684 test_database 34776 136801465577472 ❌ Failed: 3/5
ERROR 2025-06-01 11:29:06,685 test_database 34776 136801465577472 Failed tests: Database Connection, Database Performance, Database Schema
ERROR 2025-06-01 11:29:06,685 test_database 34776 136801465577472 ❌ Database testing failed!
INFO 2025-06-01 11:29:19,985 server 35190 137494121279488 🌟 Starting WorkFlo Backend Production Server...
INFO 2025-06-01 11:29:19,985 server 35190 137494121279488 🚀 Initializing WorkFlo Backend Server...
INFO 2025-06-01 11:29:19,986 server 35190 137494121279488 📋 Running: Database Connection
INFO 2025-06-01 11:29:19,986 server 35190 137494121279488 🔍 Testing database connection...
INFO 2025-06-01 11:29:19,987 server 35190 137494121279488 ✅ Database connection successful
INFO 2025-06-01 11:29:19,987 server 35190 137494121279488 ✅ Completed: Database Connection
INFO 2025-06-01 11:29:19,987 server 35190 137494121279488 📋 Running: Database Migrations
INFO 2025-06-01 11:29:19,987 server 35190 137494121279488 🔄 Running database migrations...
INFO 2025-06-01 11:29:20,759 server 35190 137494121279488 ✅ Database migrations completed successfully
INFO 2025-06-01 11:29:20,759 server 35190 137494121279488 ✅ Completed: Database Migrations
INFO 2025-06-01 11:29:20,759 server 35190 137494121279488 📋 Running: Static Files Collection
INFO 2025-06-01 11:29:20,759 server 35190 137494121279488 📁 Collecting static files...
INFO 2025-06-01 11:29:21,397 server 35190 137494121279488 ✅ Static files collected successfully
INFO 2025-06-01 11:29:21,398 server 35190 137494121279488 ✅ Completed: Static Files Collection
INFO 2025-06-01 11:29:21,398 server 35190 137494121279488 📋 Running: Superuser Creation
INFO 2025-06-01 11:29:21,398 server 35190 137494121279488 👤 Creating superuser...
INFO 2025-06-01 11:29:21,399 server 35190 137494121279488 ℹ️ Superuser already exists
INFO 2025-06-01 11:29:21,400 server 35190 137494121279488 ✅ Completed: Superuser Creation
INFO 2025-06-01 11:29:21,400 server 35190 137494121279488 📋 Running: Sample Data Population
INFO 2025-06-01 11:29:21,400 server 35190 137494121279488 📊 Populating sample data...
ERROR 2025-06-01 11:29:21,717 server 35190 137494121279488 ❌ Sample data population failed: UNIQUE constraint failed: users.employee_id
ERROR 2025-06-01 11:29:21,717 server 35190 137494121279488 ❌ Failed: Sample Data Population
INFO 2025-06-01 11:29:21,718 server 35190 137494121279488 📋 Running: Health Check
INFO 2025-06-01 11:29:21,718 server 35190 137494121279488 🏥 Performing system health check...
ERROR 2025-06-01 11:29:22,795 monitoring 35190 137494121279488 Cache health check failed: AbstractConnection.__init__() got an unexpected keyword argument 'CLIENT_CLASS'
ERROR 2025-06-01 11:29:22,798 server 35190 137494121279488 ❌ System health check failed
INFO 2025-06-01 11:29:22,798 server 35190 137494121279488   database: healthy
INFO 2025-06-01 11:29:22,798 server 35190 137494121279488   system_resources: warning
INFO 2025-06-01 11:29:22,798 server 35190 137494121279488   application: healthy
INFO 2025-06-01 11:29:22,798 server 35190 137494121279488   cache: critical
ERROR 2025-06-01 11:29:22,798 server 35190 137494121279488 ❌ Failed: Health Check
INFO 2025-06-01 11:29:22,799 server 35190 137494121279488 📋 Running: Cron Jobs Setup
INFO 2025-06-01 11:29:22,799 server 35190 137494121279488 ⏰ Setting up cron jobs...
INFO 2025-06-01 11:29:22,799 server 35190 137494121279488 ✅ Cron scheduler started
INFO 2025-06-01 11:29:22,799 server 35190 137494121279488 ✅ Completed: Cron Jobs Setup
ERROR 2025-06-01 11:29:22,799 server 35190 137494121279488 ❌ Server initialization failed. Failed steps: Sample Data Population, Health Check
ERROR 2025-06-01 11:29:22,800 server 35190 137494121279488 ❌ Server initialization failed. Exiting.
INFO 2025-06-01 11:29:57,118 server 35756 127227290898432 🌟 Starting WorkFlo Backend Production Server...
INFO 2025-06-01 11:29:57,119 server 35756 127227290898432 🚀 Initializing WorkFlo Backend Server...
INFO 2025-06-01 11:29:57,120 server 35756 127227290898432 📋 Running: Database Connection
INFO 2025-06-01 11:29:57,120 server 35756 127227290898432 🔍 Testing database connection...
INFO 2025-06-01 11:29:57,121 server 35756 127227290898432 ✅ Database connection successful
INFO 2025-06-01 11:29:57,122 server 35756 127227290898432 ✅ Completed: Database Connection
INFO 2025-06-01 11:29:57,122 server 35756 127227290898432 📋 Running: Database Migrations
INFO 2025-06-01 11:29:57,122 server 35756 127227290898432 🔄 Running database migrations...
INFO 2025-06-01 11:29:57,840 server 35756 127227290898432 ✅ Database migrations completed successfully
INFO 2025-06-01 11:29:57,841 server 35756 127227290898432 ✅ Completed: Database Migrations
INFO 2025-06-01 11:29:57,842 server 35756 127227290898432 📋 Running: Static Files Collection
INFO 2025-06-01 11:29:57,842 server 35756 127227290898432 📁 Collecting static files...
INFO 2025-06-01 11:29:58,013 server 35756 127227290898432 ✅ Static files collected successfully
INFO 2025-06-01 11:29:58,014 server 35756 127227290898432 ✅ Completed: Static Files Collection
INFO 2025-06-01 11:29:58,014 server 35756 127227290898432 📋 Running: Superuser Creation
INFO 2025-06-01 11:29:58,014 server 35756 127227290898432 👤 Creating superuser...
INFO 2025-06-01 11:29:58,017 server 35756 127227290898432 ℹ️ Superuser already exists
INFO 2025-06-01 11:29:58,017 server 35756 127227290898432 ✅ Completed: Superuser Creation
INFO 2025-06-01 11:29:58,017 server 35756 127227290898432 📋 Running: Sample Data Population
INFO 2025-06-01 11:29:58,017 server 35756 127227290898432 📊 Populating sample data...
ERROR 2025-06-01 11:29:58,136 server 35756 127227290898432 ❌ Sample data population failed: UNIQUE constraint failed: users.employee_id
ERROR 2025-06-01 11:29:58,144 server 35756 127227290898432 ❌ Failed: Sample Data Population
INFO 2025-06-01 11:29:58,145 server 35756 127227290898432 📋 Running: Health Check
INFO 2025-06-01 11:29:58,145 server 35756 127227290898432 🏥 Performing system health check...
INFO 2025-06-01 11:29:59,159 server 35756 127227290898432 ✅ System health check passed
INFO 2025-06-01 11:29:59,160 server 35756 127227290898432   database: healthy
INFO 2025-06-01 11:29:59,160 server 35756 127227290898432   system_resources: healthy
INFO 2025-06-01 11:29:59,160 server 35756 127227290898432   application: healthy
INFO 2025-06-01 11:29:59,160 server 35756 127227290898432   cache: healthy
INFO 2025-06-01 11:29:59,160 server 35756 127227290898432 ✅ Completed: Health Check
INFO 2025-06-01 11:29:59,160 server 35756 127227290898432 📋 Running: Cron Jobs Setup
INFO 2025-06-01 11:29:59,160 server 35756 127227290898432 ⏰ Setting up cron jobs...
INFO 2025-06-01 11:29:59,161 server 35756 127227290898432 ✅ Cron scheduler started
INFO 2025-06-01 11:29:59,161 server 35756 127227290898432 ✅ Completed: Cron Jobs Setup
ERROR 2025-06-01 11:29:59,161 server 35756 127227290898432 ❌ Server initialization failed. Failed steps: Sample Data Population
ERROR 2025-06-01 11:29:59,161 server 35756 127227290898432 ❌ Server initialization failed. Exiting.
