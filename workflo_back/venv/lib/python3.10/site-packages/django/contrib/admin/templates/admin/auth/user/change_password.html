{% extends "admin/base_site.html" %}
{% load i18n static %}
{% load admin_urls %}

{% block title %}{% if form.errors %}{% translate "Error:" %} {% endif %}{{ block.super }}{% endblock %}
{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" href="{% static "admin/css/forms.css" %}">
  <link rel="stylesheet" href="{% static 'admin/css/unusable_password_field.css' %}">
{% endblock %}
{% block bodyclass %}{{ block.super }} {{ opts.app_label }}-{{ opts.model_name }} change-form{% endblock %}
{% if not is_popup %}
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% translate 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
&rsaquo; <a href="{% url opts|admin_urlname:'change' original.pk|admin_urlquote %}">{{ original|truncatewords:"18" }}</a>
&rsaquo; {% if form.user.has_usable_password %}{% translate 'Change password' %}{% else %}{% translate 'Set password' %}{% endif %}
</div>
{% endblock %}
{% endif %}
{% block content %}<div id="content-main">
<form{% if form_url %} action="{{ form_url }}"{% endif %} method="post" id="{{ opts.model_name }}_form">{% csrf_token %}{% block form_top %}{% endblock %}
<input type="text" name="username" value="{{ original.get_username }}" class="hidden">
<div>
{% if is_popup %}<input type="hidden" name="{{ is_popup_var }}" value="1">{% endif %}
{% if form.errors %}
    <p class="errornote">
    {% blocktranslate count counter=form.errors.items|length %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktranslate %}
    </p>
{% endif %}

<p>{% blocktranslate with username=original %}Enter a new password for the user <strong>{{ username }}</strong>.{% endblocktranslate %}</p>
{% if not form.user.has_usable_password %}
  <p>{% blocktranslate %}This action will <strong>enable</strong> password-based authentication for this user.{% endblocktranslate %}</p>
{% endif %}

<fieldset class="module aligned">

<div class="form-row">
  {{ form.usable_password.errors }}
  <div class="flex-container">{{ form.usable_password.label_tag }} {{ form.usable_password }}</div>
  {% if form.usable_password.help_text %}
  <div class="help"{% if form.usable_password.id_for_label %} id="{{ form.usable_password.id_for_label }}_helptext"{% endif %}>
    <p>{{ form.usable_password.help_text|safe }}</p>
  </div>
  {% endif %}
</div>

<div class="form-row field-password1">
  {{ form.password1.errors }}
  <div class="flex-container">{{ form.password1.label_tag }} {{ form.password1 }}</div>
  {% if form.password1.help_text %}
  <div class="help"{% if form.password1.id_for_label %} id="{{ form.password1.id_for_label }}_helptext"{% endif %}>{{ form.password1.help_text|safe }}</div>
  {% endif %}
</div>

<div class="form-row field-password2">
  {{ form.password2.errors }}
  <div class="flex-container">{{ form.password2.label_tag }} {{ form.password2 }}</div>
  {% if form.password2.help_text %}
  <div class="help"{% if form.password2.id_for_label %} id="{{ form.password2.id_for_label }}_helptext"{% endif %}>{{ form.password2.help_text|safe }}</div>
  {% endif %}
</div>

</fieldset>

<div class="submit-row">
  {% if form.user.has_usable_password %}
  <input type="submit" name="set-password" value="{% translate 'Change password' %}" class="default set-password">
  <input type="submit" name="unset-password" value="{% translate 'Disable password-based authentication' %}" class="unset-password">
  {% else %}
  <input type="submit" name="set-password" value="{% translate 'Enable password-based authentication' %}" class="default set-password">
  {% endif %}
</div>

</div>
</form></div>
<script src="{% static 'admin/js/unusable_password_field.js' %}" defer></script>
{% endblock %}
