"""
Middleware for WorkFlo Backend
Request validation, error handling, and security middleware
"""

import json
import logging
import time
from django.http import JsonResponse
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.core.exceptions import ValidationError
from django.db import transaction
from rest_framework import status
from .exceptions import SystemMaintenanceError, RateLimitExceededError

logger = logging.getLogger(__name__)


class RequestLoggingMiddleware(MiddlewareMixin):
    """Middleware to log all API requests and responses"""
    
    def process_request(self, request):
        """Log incoming requests"""
        
        # Skip logging for certain paths
        skip_paths = ['/health/', '/admin/', '/static/', '/media/']
        if any(request.path.startswith(path) for path in skip_paths):
            return None
        
        # Record request start time
        request._start_time = time.time()
        
        # Log request details
        user = getattr(request, 'user', None)
        user_info = user.email if user and user.is_authenticated else 'Anonymous'
        
        logger.info(
            f"Request: {request.method} {request.path} | "
            f"User: {user_info} | "
            f"IP: {self.get_client_ip(request)} | "
            f"User-Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')}"
        )
        
        return None
    
    def process_response(self, request, response):
        """Log response details"""
        
        # Skip logging for certain paths
        skip_paths = ['/health/', '/admin/', '/static/', '/media/']
        if any(request.path.startswith(path) for path in skip_paths):
            return response
        
        # Calculate response time
        if hasattr(request, '_start_time'):
            response_time = (time.time() - request._start_time) * 1000  # Convert to milliseconds
        else:
            response_time = 0
        
        # Log response details
        logger.info(
            f"Response: {request.method} {request.path} | "
            f"Status: {response.status_code} | "
            f"Time: {response_time:.2f}ms"
        )
        
        # Log slow requests
        if response_time > 1000:  # More than 1 second
            logger.warning(
                f"Slow request detected: {request.method} {request.path} | "
                f"Time: {response_time:.2f}ms"
            )
        
        return response
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class ErrorHandlingMiddleware(MiddlewareMixin):
    """Middleware for global error handling"""
    
    def process_exception(self, request, exception):
        """Handle unhandled exceptions"""
        
        # Log the exception
        user = getattr(request, 'user', None)
        user_info = user.email if user and user.is_authenticated else 'Anonymous'
        
        logger.error(
            f"Unhandled exception in {request.method} {request.path} | "
            f"User: {user_info} | "
            f"Exception: {exception.__class__.__name__}: {str(exception)}",
            exc_info=True
        )
        
        # Return JSON error response for API requests
        if request.path.startswith('/api/'):
            return JsonResponse({
                'success': False,
                'error': 'An unexpected error occurred',
                'error_code': 'internal_server_error',
                'timestamp': timezone.now().isoformat()
            }, status=500)
        
        # Let Django handle non-API requests
        return None


class SecurityHeadersMiddleware(MiddlewareMixin):
    """Middleware to add security headers"""
    
    def process_response(self, request, response):
        """Add security headers to response"""
        
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # Add CORS headers for API requests
        if request.path.startswith('/api/'):
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        
        return response


class MaintenanceModeMiddleware(MiddlewareMixin):
    """Middleware to handle maintenance mode"""
    
    def process_request(self, request):
        """Check if system is in maintenance mode"""
        
        # Skip maintenance check for admin and health endpoints
        skip_paths = ['/admin/', '/health/', '/api/system/health/']
        if any(request.path.startswith(path) for path in skip_paths):
            return None
        
        # Check maintenance mode setting
        try:
            from .models.system import SystemSetting
            maintenance_mode = SystemSetting.objects.filter(
                key='maintenance_mode',
                value='true'
            ).exists()
            
            if maintenance_mode:
                # Allow superusers to access during maintenance
                user = getattr(request, 'user', None)
                if user and user.is_authenticated and user.is_superuser:
                    return None
                
                # Return maintenance response for API requests
                if request.path.startswith('/api/'):
                    return JsonResponse({
                        'success': False,
                        'error': 'System is currently under maintenance',
                        'error_code': 'system_maintenance',
                        'timestamp': timezone.now().isoformat()
                    }, status=503)
                
                # Redirect to maintenance page for web requests
                from django.shortcuts import render
                return render(request, 'maintenance.html', status=503)
        
        except Exception:
            # If we can't check maintenance mode, allow the request
            pass
        
        return None


class RateLimitingMiddleware(MiddlewareMixin):
    """Simple rate limiting middleware"""
    
    def __init__(self, get_response):
        super().__init__(get_response)
        self.request_counts = {}  # In production, use Redis or database
        self.rate_limit = 100  # requests per minute
        self.window_size = 60  # seconds
    
    def process_request(self, request):
        """Check rate limits"""
        
        # Skip rate limiting for certain paths
        skip_paths = ['/admin/', '/static/', '/media/']
        if any(request.path.startswith(path) for path in skip_paths):
            return None
        
        # Get client identifier
        client_id = self.get_client_identifier(request)
        current_time = time.time()
        
        # Clean old entries
        self.cleanup_old_entries(current_time)
        
        # Check rate limit
        if client_id in self.request_counts:
            request_times = self.request_counts[client_id]
            recent_requests = [t for t in request_times if current_time - t < self.window_size]
            
            if len(recent_requests) >= self.rate_limit:
                logger.warning(f"Rate limit exceeded for {client_id}")
                
                if request.path.startswith('/api/'):
                    return JsonResponse({
                        'success': False,
                        'error': 'Rate limit exceeded',
                        'error_code': 'rate_limit_exceeded',
                        'timestamp': timezone.now().isoformat()
                    }, status=429)
        
        # Record this request
        if client_id not in self.request_counts:
            self.request_counts[client_id] = []
        self.request_counts[client_id].append(current_time)
        
        return None
    
    def get_client_identifier(self, request):
        """Get unique identifier for client"""
        # Use user ID if authenticated, otherwise IP address
        user = getattr(request, 'user', None)
        if user and user.is_authenticated:
            return f"user_{user.id}"
        else:
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip = x_forwarded_for.split(',')[0]
            else:
                ip = request.META.get('REMOTE_ADDR')
            return f"ip_{ip}"
    
    def cleanup_old_entries(self, current_time):
        """Remove old entries from request counts"""
        for client_id in list(self.request_counts.keys()):
            self.request_counts[client_id] = [
                t for t in self.request_counts[client_id]
                if current_time - t < self.window_size
            ]
            if not self.request_counts[client_id]:
                del self.request_counts[client_id]


class DatabaseTransactionMiddleware(MiddlewareMixin):
    """Middleware to handle database transactions for API requests"""
    
    def process_request(self, request):
        """Start transaction for write operations"""
        
        # Only handle API requests
        if not request.path.startswith('/api/'):
            return None
        
        # Only handle write operations
        if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            transaction.set_autocommit(False)
        
        return None
    
    def process_response(self, request, response):
        """Commit or rollback transaction based on response status"""
        
        # Only handle API requests
        if not request.path.startswith('/api/'):
            return response
        
        # Only handle write operations
        if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            try:
                if response.status_code < 400:
                    transaction.commit()
                else:
                    transaction.rollback()
            except Exception as e:
                logger.error(f"Transaction error: {str(e)}")
                transaction.rollback()
            finally:
                transaction.set_autocommit(True)
        
        return response
    
    def process_exception(self, request, exception):
        """Rollback transaction on exception"""
        
        # Only handle API requests
        if not request.path.startswith('/api/'):
            return None
        
        # Only handle write operations
        if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            try:
                transaction.rollback()
            except Exception as e:
                logger.error(f"Transaction rollback error: {str(e)}")
            finally:
                transaction.set_autocommit(True)
        
        return None


class RequestValidationMiddleware(MiddlewareMixin):
    """Middleware for basic request validation"""
    
    def process_request(self, request):
        """Validate incoming requests"""
        
        # Only validate API requests
        if not request.path.startswith('/api/'):
            return None
        
        # Validate content type for POST/PUT/PATCH requests
        if request.method in ['POST', 'PUT', 'PATCH']:
            content_type = request.META.get('CONTENT_TYPE', '')
            
            if not content_type.startswith('application/json') and not content_type.startswith('multipart/form-data'):
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid content type',
                    'error_code': 'invalid_content_type',
                    'details': {'expected': 'application/json or multipart/form-data'},
                    'timestamp': timezone.now().isoformat()
                }, status=400)
        
        # Validate JSON for JSON requests
        if request.content_type == 'application/json' and request.body:
            try:
                json.loads(request.body)
            except json.JSONDecodeError:
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid JSON format',
                    'error_code': 'invalid_json',
                    'timestamp': timezone.now().isoformat()
                }, status=400)
        
        return None


class AuditLogMiddleware(MiddlewareMixin):
    """Middleware to create audit logs for important actions"""
    
    def process_response(self, request, response):
        """Create audit log for certain actions"""
        
        # Only log API requests
        if not request.path.startswith('/api/'):
            return response
        
        # Only log write operations that succeeded
        if request.method in ['POST', 'PUT', 'PATCH', 'DELETE'] and response.status_code < 400:
            try:
                self.create_audit_log(request, response)
            except Exception as e:
                logger.error(f"Failed to create audit log: {str(e)}")
        
        return response
    
    def create_audit_log(self, request, response):
        """Create audit log entry"""
        
        from .models.system import AuditLog
        
        user = getattr(request, 'user', None)
        if not user or not user.is_authenticated:
            return
        
        # Determine action based on method and path
        action = self.get_action_description(request)
        
        # Get client IP
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')
        
        # Create audit log
        AuditLog.objects.create(
            user=user,
            action=action,
            resource_type=self.get_resource_type(request.path),
            resource_id=self.get_resource_id(request.path),
            ip_address=ip_address,
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            timestamp=timezone.now()
        )
    
    def get_action_description(self, request):
        """Get action description from request"""
        
        method_actions = {
            'POST': 'Created',
            'PUT': 'Updated',
            'PATCH': 'Updated',
            'DELETE': 'Deleted'
        }
        
        action = method_actions.get(request.method, 'Unknown')
        resource = self.get_resource_type(request.path)
        
        return f"{action} {resource}"
    
    def get_resource_type(self, path):
        """Extract resource type from path"""
        
        # Remove /api/ prefix and extract resource
        if path.startswith('/api/'):
            path = path[5:]
        
        # Get first part of path
        parts = path.split('/')
        if parts:
            return parts[0].replace('-', ' ').title()
        
        return 'Unknown'
    
    def get_resource_id(self, path):
        """Extract resource ID from path"""
        
        # Look for numeric ID in path
        parts = path.split('/')
        for part in parts:
            if part.isdigit():
                return part
        
        return None
