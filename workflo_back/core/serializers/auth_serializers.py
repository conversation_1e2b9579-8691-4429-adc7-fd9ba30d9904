from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from django.contrib.auth.password_validation import validate_password
from ..models.auth import User, UserSession, PasswordResetToken


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for User model - read operations
    """
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'username', 'first_name', 'last_name', 'full_name',
            'employee_id', 'phone_number', 'profile_picture', 'role',
            'is_active', 'date_joined', 'last_login', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'date_joined', 'created_at', 'updated_at']
    
    def get_full_name(self, obj):
        return f"{obj.first_name} {obj.last_name}"


class UserCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for User creation
    """
    password = serializers.Char<PERSON>ield(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'email', 'username', 'first_name', 'last_name', 'employee_id',
            'phone_number', 'role', 'password', 'password_confirm'
        ]
    
    def validate(self, attrs):
        """Validate password confirmation"""
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        """Create user with hashed password"""
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user


class UserUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for User updates
    """
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'phone_number', 'profile_picture',
            'role', 'is_active'
        ]
    
    def validate_role(self, value):
        """Validate role changes"""
        user = self.instance
        request_user = self.context['request'].user
        
        # Only admins can change roles
        if request_user.role != 'admin' and user.role != value:
            raise serializers.ValidationError("Only admins can change user roles")
        
        return value


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """
    Custom JWT token serializer with additional user information
    """
    username_field = 'email'
    
    def validate(self, attrs):
        """Add user information to token response"""
        data = super().validate(attrs)
        
        # Add user information
        data['user'] = {
            'id': self.user.id,
            'email': self.user.email,
            'first_name': self.user.first_name,
            'last_name': self.user.last_name,
            'role': self.user.role,
            'employee_id': self.user.employee_id,
        }
        
        return data
    
    @classmethod
    def get_token(cls, user):
        """Add custom claims to token"""
        token = super().get_token(user)
        
        # Add custom claims
        token['email'] = user.email
        token['role'] = user.role
        token['employee_id'] = user.employee_id
        
        return token


class UserSessionSerializer(serializers.ModelSerializer):
    """
    Serializer for UserSession model
    """
    user_email = serializers.CharField(source='user.email', read_only=True)
    
    class Meta:
        model = UserSession
        fields = [
            'id', 'user', 'user_email', 'expires_at', 'created_at',
            'last_used', 'ip_address', 'user_agent'
        ]
        read_only_fields = ['id', 'created_at']


class PasswordResetSerializer(serializers.Serializer):
    """
    Serializer for password reset request
    """
    email = serializers.EmailField()
    
    def validate_email(self, value):
        """Validate email format"""
        return value.lower()


class PasswordResetTokenSerializer(serializers.ModelSerializer):
    """
    Serializer for PasswordResetToken model
    """
    user_email = serializers.CharField(source='user.email', read_only=True)
    
    class Meta:
        model = PasswordResetToken
        fields = [
            'id', 'user', 'user_email', 'token', 'expires_at',
            'used', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
