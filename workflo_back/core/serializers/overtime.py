"""
Overtime Management Serializers
Comprehensive serializers for overtime-related models
"""

from rest_framework import serializers
from ..models.overtime import (
    OvertimeType, OvertimeRequest, OvertimeRecord,
    OvertimeApprovalWorkflow, OvertimeBudget, OvertimeCalculation
)
from ..models.auth import User
from ..models.organization import Department


class OvertimeTypeSerializer(serializers.ModelSerializer):
    """Serializer for OvertimeType model"""
    
    class Meta:
        model = OvertimeType
        fields = [
            'id', 'name', 'description', 'rate_multiplier',
            'max_hours_per_day', 'max_hours_per_week', 'max_hours_per_month',
            'requires_pre_approval', 'auto_approve_threshold', 'is_active',
            'created_at', 'updated_at', 'created_by', 'updated_by'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by', 'updated_by']

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        validated_data['updated_by'] = self.context['request'].user
        return super().update(instance, validated_data)


class OvertimeRequestSerializer(serializers.ModelSerializer):
    """Serializer for OvertimeRequest model"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    overtime_type_name = serializers.CharField(source='overtime_type.name', read_only=True)
    
    class Meta:
        model = OvertimeRequest
        fields = [
            'id', 'employee', 'employee_name', 'overtime_type', 'overtime_type_name',
            'request_date', 'planned_start_time', 'planned_end_time', 'planned_hours',
            'reason', 'justification', 'project_code', 'department_approval_required',
            'status', 'requested_by', 'supervisor_approved_by', 'supervisor_approved_at',
            'supervisor_comments', 'admin_approved_by', 'admin_approved_at', 'admin_comments',
            'final_approved_by', 'final_approved_at', 'rejection_reason',
            'actual_start_time', 'actual_end_time', 'actual_hours', 'completion_notes',
            'completed_by', 'completed_at', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'employee_name', 'overtime_type_name', 'supervisor_approved_by',
            'supervisor_approved_at', 'admin_approved_by', 'admin_approved_at',
            'final_approved_by', 'final_approved_at', 'completed_by', 'completed_at',
            'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        validated_data['employee'] = self.context['request'].user
        validated_data['requested_by'] = self.context['request'].user
        return super().create(validated_data)


class OvertimeRecordSerializer(serializers.ModelSerializer):
    """Serializer for OvertimeRecord model"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    overtime_type_name = serializers.CharField(source='overtime_type.name', read_only=True)
    
    class Meta:
        model = OvertimeRecord
        fields = [
            'id', 'employee', 'employee_name', 'overtime_request', 'overtime_type',
            'overtime_type_name', 'attendance_record', 'overtime_date', 'start_time',
            'end_time', 'total_hours', 'rate_multiplier', 'regular_hourly_rate',
            'overtime_hourly_rate', 'total_amount', 'currency', 'overtime_category',
            'is_emergency', 'is_pre_approved', 'status', 'approved_by', 'approved_at',
            'approval_comments', 'pay_cycle', 'included_in_payroll', 'payroll_processed_at',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'employee_name', 'overtime_type_name', 'overtime_hourly_rate',
            'total_amount', 'approved_by', 'approved_at', 'payroll_processed_at',
            'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class OvertimeApprovalWorkflowSerializer(serializers.ModelSerializer):
    """Serializer for OvertimeApprovalWorkflow model"""
    department_name = serializers.CharField(source='department.name', read_only=True)
    
    class Meta:
        model = OvertimeApprovalWorkflow
        fields = [
            'id', 'name', 'description', 'department', 'department_name',
            'requires_supervisor_approval', 'requires_admin_approval', 'requires_hr_approval',
            'supervisor_approval_threshold', 'admin_approval_threshold', 'auto_approval_threshold',
            'approval_deadline_hours', 'advance_notice_hours', 'escalation_enabled',
            'escalation_hours', 'escalate_to_admin', 'escalate_to_hr',
            'notify_employee', 'notify_supervisor', 'notify_admin', 'notify_hr',
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'department_name', 'created_at', 'updated_at']

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class OvertimeBudgetSerializer(serializers.ModelSerializer):
    """Serializer for OvertimeBudget model"""
    department_name = serializers.CharField(source='department.name', read_only=True)
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    remaining_hours = serializers.DecimalField(max_digits=6, decimal_places=2, read_only=True)
    remaining_amount = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)
    utilization_percentage = serializers.SerializerMethodField()
    
    class Meta:
        model = OvertimeBudget
        fields = [
            'id', 'department', 'department_name', 'employee', 'employee_name',
            'budget_year', 'budget_month', 'allocated_hours', 'allocated_amount',
            'used_hours', 'used_amount', 'remaining_hours', 'remaining_amount',
            'utilization_percentage', 'warning_threshold_percentage', 'block_threshold_percentage',
            'is_active', 'budget_exceeded', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'department_name', 'employee_name', 'used_hours', 'used_amount',
            'remaining_hours', 'remaining_amount', 'utilization_percentage',
            'budget_exceeded', 'created_at', 'updated_at'
        ]

    def get_utilization_percentage(self, obj):
        """Calculate budget utilization percentage"""
        if obj.allocated_amount > 0:
            return round((obj.used_amount / obj.allocated_amount) * 100, 2)
        return 0

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class OvertimeCalculationSerializer(serializers.ModelSerializer):
    """Serializer for OvertimeCalculation model"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    overtime_record_details = serializers.CharField(source='overtime_record.__str__', read_only=True)
    
    class Meta:
        model = OvertimeCalculation
        fields = [
            'id', 'employee', 'employee_name', 'overtime_record', 'overtime_record_details',
            'pay_cycle', 'base_hourly_rate', 'overtime_rate_multiplier', 'calculated_overtime_rate',
            'total_overtime_hours', 'regular_hours_equivalent', 'gross_overtime_amount',
            'overtime_tax_rate', 'overtime_tax_amount', 'overtime_nssf_amount',
            'overtime_nhif_amount', 'overtime_housing_levy', 'total_overtime_deductions',
            'net_overtime_amount', 'calculation_date', 'calculation_method',
            'currency', 'exchange_rate', 'status', 'approved_by', 'approved_at',
            'included_in_payroll', 'payroll_record', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'employee_name', 'overtime_record_details', 'calculated_overtime_rate',
            'total_overtime_deductions', 'net_overtime_amount', 'approved_by',
            'approved_at', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

    def validate(self, data):
        """Validate overtime calculation data"""
        # Calculate derived fields
        if 'base_hourly_rate' in data and 'overtime_rate_multiplier' in data:
            data['calculated_overtime_rate'] = data['base_hourly_rate'] * data['overtime_rate_multiplier']
        
        if 'gross_overtime_amount' in data:
            # Calculate deductions
            gross_amount = data['gross_overtime_amount']
            tax_amount = gross_amount * (data.get('overtime_tax_rate', 0) / 100)
            nssf_amount = gross_amount * 0.06  # 6% NSSF
            nhif_amount = gross_amount * 0.0275  # 2.75% NHIF
            housing_levy = gross_amount * 0.015  # 1.5% Housing Levy
            
            total_deductions = tax_amount + nssf_amount + nhif_amount + housing_levy
            
            data['overtime_tax_amount'] = tax_amount
            data['overtime_nssf_amount'] = nssf_amount
            data['overtime_nhif_amount'] = nhif_amount
            data['overtime_housing_levy'] = housing_levy
            data['total_overtime_deductions'] = total_deductions
            data['net_overtime_amount'] = gross_amount - total_deductions
        
        return data
