"""
Training & Development Serializers
Comprehensive serializers for training-related models
"""

from rest_framework import serializers
from ..models.training import (
    TrainingModule, TrainingVenue, EmployeeTrainingAssignment,
    TrainingSession, TrainingSessionParticipant
)
from ..models.auth import User


class TrainingModuleSerializer(serializers.ModelSerializer):
    """Serializer for TrainingModule model"""
    instructor_name = serializers.CharField(max_length=100, required=False)
    instructor_email = serializers.EmailField(required=False)
    
    class Meta:
        model = TrainingModule
        fields = [
            'id', 'title', 'description', 'content', 'duration_hours',
            'difficulty_level', 'category', 'prerequisites', 'learning_objectives',
            'instructor_name', 'instructor_email', 'materials_url',
            'is_mandatory', 'is_active', 'created_at', 'updated_at', 'created_by'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

    def update(self, instance, validated_data):
        validated_data['updated_by'] = self.context['request'].user
        return super().update(instance, validated_data)


class TrainingVenueSerializer(serializers.ModelSerializer):
    """Serializer for TrainingVenue model"""
    equipment = serializers.ListField(
        child=serializers.CharField(max_length=100),
        required=False,
        allow_empty=True
    )
    facilities = serializers.ListField(
        child=serializers.CharField(max_length=100),
        required=False,
        allow_empty=True
    )
    
    class Meta:
        model = TrainingVenue
        fields = [
            'id', 'name', 'location', 'address', 'capacity', 'equipment',
            'facilities', 'hourly_rate', 'contact_person', 'contact_phone',
            'contact_email', 'status', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_capacity(self, value):
        """Validate venue capacity"""
        if value is not None and value <= 0:
            raise serializers.ValidationError("Capacity must be greater than 0")
        return value

    def validate_hourly_rate(self, value):
        """Validate hourly rate"""
        if value is not None and value < 0:
            raise serializers.ValidationError("Hourly rate cannot be negative")
        return value


class EmployeeTrainingAssignmentSerializer(serializers.ModelSerializer):
    """Serializer for EmployeeTrainingAssignment model"""
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    training_module_title = serializers.CharField(source='training_module.title', read_only=True)
    venue_name = serializers.CharField(source='venue.name', read_only=True)
    assigned_by_name = serializers.CharField(source='assigned_by.get_full_name', read_only=True)
    
    class Meta:
        model = EmployeeTrainingAssignment
        fields = [
            'id', 'employee', 'employee_name', 'training_module', 'training_module_title',
            'venue', 'venue_name', 'assigned_by', 'assigned_by_name', 'assigned_date',
            'start_date', 'end_date', 'due_date', 'status', 'progress_percentage',
            'completion_date', 'score', 'feedback', 'certificate_url',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'employee_name', 'training_module_title', 'venue_name',
            'assigned_by_name', 'assigned_by', 'assigned_date', 'created_at', 'updated_at'
        ]

    def create(self, validated_data):
        validated_data['assigned_by'] = self.context['request'].user
        return super().create(validated_data)

    def validate_progress_percentage(self, value):
        """Validate progress percentage"""
        if value < 0 or value > 100:
            raise serializers.ValidationError("Progress percentage must be between 0 and 100")
        return value

    def validate_score(self, value):
        """Validate score"""
        if value is not None and (value < 0 or value > 100):
            raise serializers.ValidationError("Score must be between 0 and 100")
        return value

    def validate(self, data):
        """Validate assignment dates"""
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        due_date = data.get('due_date')
        
        if start_date and end_date and start_date > end_date:
            raise serializers.ValidationError("Start date cannot be after end date")
        
        if start_date and due_date and start_date > due_date:
            raise serializers.ValidationError("Start date cannot be after due date")
        
        return data


class TrainingSessionSerializer(serializers.ModelSerializer):
    """Serializer for TrainingSession model"""
    training_module_title = serializers.CharField(source='training_module.title', read_only=True)
    venue_name = serializers.CharField(source='venue.name', read_only=True)
    instructor_name = serializers.CharField(source='instructor.get_full_name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    available_spots = serializers.SerializerMethodField()
    
    class Meta:
        model = TrainingSession
        fields = [
            'id', 'training_module', 'training_module_title', 'venue', 'venue_name',
            'instructor', 'instructor_name', 'title', 'start_datetime', 'end_datetime',
            'max_participants', 'current_participants', 'available_spots', 'status',
            'notes', 'created_at', 'created_by', 'created_by_name'
        ]
        read_only_fields = [
            'id', 'training_module_title', 'venue_name', 'instructor_name',
            'created_by_name', 'current_participants', 'available_spots',
            'created_at', 'created_by'
        ]

    def get_available_spots(self, obj):
        """Calculate available spots"""
        if obj.max_participants:
            return max(0, obj.max_participants - obj.current_participants)
        return None

    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)

    def validate(self, data):
        """Validate session dates and capacity"""
        start_datetime = data.get('start_datetime')
        end_datetime = data.get('end_datetime')
        max_participants = data.get('max_participants')
        
        if start_datetime and end_datetime and start_datetime >= end_datetime:
            raise serializers.ValidationError("Start datetime must be before end datetime")
        
        if max_participants is not None and max_participants <= 0:
            raise serializers.ValidationError("Max participants must be greater than 0")
        
        # Check venue availability if venue is specified
        venue = data.get('venue')
        if venue and start_datetime and end_datetime:
            # Check for overlapping sessions in the same venue
            overlapping_sessions = TrainingSession.objects.filter(
                venue=venue,
                status__in=['scheduled', 'in_progress'],
                start_datetime__lt=end_datetime,
                end_datetime__gt=start_datetime
            )
            
            if self.instance:
                overlapping_sessions = overlapping_sessions.exclude(id=self.instance.id)
            
            if overlapping_sessions.exists():
                raise serializers.ValidationError("Venue is not available during the specified time")
        
        return data


class TrainingSessionParticipantSerializer(serializers.ModelSerializer):
    """Serializer for TrainingSessionParticipant model"""
    session_title = serializers.CharField(source='session.title', read_only=True)
    employee_name = serializers.CharField(source='employee.get_full_name', read_only=True)
    session_start_datetime = serializers.DateTimeField(source='session.start_datetime', read_only=True)
    session_end_datetime = serializers.DateTimeField(source='session.end_datetime', read_only=True)
    
    class Meta:
        model = TrainingSessionParticipant
        fields = [
            'id', 'session', 'session_title', 'session_start_datetime', 'session_end_datetime',
            'employee', 'employee_name', 'attendance_status', 'completion_status',
            'score', 'feedback', 'certificate_issued', 'registered_at'
        ]
        read_only_fields = [
            'id', 'session_title', 'employee_name', 'session_start_datetime',
            'session_end_datetime', 'registered_at'
        ]

    def validate_score(self, value):
        """Validate score"""
        if value is not None and (value < 0 or value > 100):
            raise serializers.ValidationError("Score must be between 0 and 100")
        return value

    def validate(self, data):
        """Validate participant data"""
        session = data.get('session')
        employee = data.get('employee')
        
        # Check if employee is already registered for this session
        if session and employee:
            existing_participant = TrainingSessionParticipant.objects.filter(
                session=session,
                employee=employee
            )
            
            if self.instance:
                existing_participant = existing_participant.exclude(id=self.instance.id)
            
            if existing_participant.exists():
                raise serializers.ValidationError("Employee is already registered for this session")
        
        # Check session capacity
        if session and session.max_participants:
            if session.current_participants >= session.max_participants:
                raise serializers.ValidationError("Training session is full")
        
        return data

    def create(self, validated_data):
        """Create participant and update session count"""
        participant = super().create(validated_data)
        
        # Update session participant count
        session = participant.session
        session.current_participants = session.participants.filter(
            attendance_status__in=['registered', 'attended']
        ).count()
        session.save()
        
        return participant

    def update(self, instance, validated_data):
        """Update participant and session count if needed"""
        old_status = instance.attendance_status
        participant = super().update(instance, validated_data)
        new_status = participant.attendance_status
        
        # Update session count if attendance status changed
        if old_status != new_status:
            session = participant.session
            session.current_participants = session.participants.filter(
                attendance_status__in=['registered', 'attended']
            ).count()
            session.save()
        
        return participant
