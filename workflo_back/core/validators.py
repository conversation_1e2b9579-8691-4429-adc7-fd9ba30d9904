"""
Comprehensive Validation System for WorkFlo Backend
Custom validators for business logic, data integrity, and security
"""

import re
import datetime
from decimal import Decimal
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.utils import timezone
from django.contrib.auth.password_validation import validate_password
from django.core.validators import EmailValidator


class KenyanValidators:
    """Validators specific to Kenyan business requirements"""
    
    @staticmethod
    def validate_kra_pin(value):
        """Validate Kenyan KRA PIN format (A123456789Z)"""
        if not value:
            return
        
        pattern = r'^[A-Z]\d{9}[A-Z]$'
        if not re.match(pattern, value.upper()):
            raise ValidationError(
                'KRA PIN must be in format A123456789Z (letter + 9 digits + letter)',
                code='invalid_kra_pin'
            )
    
    @staticmethod
    def validate_national_id(value):
        """Validate Kenyan National ID format"""
        if not value:
            return
        
        # Remove any spaces or dashes
        clean_value = re.sub(r'[\s-]', '', str(value))
        
        if not clean_value.isdigit() or len(clean_value) != 8:
            raise ValidationError(
                'National ID must be 8 digits',
                code='invalid_national_id'
            )
    
    @staticmethod
    def validate_phone_number(value):
        """Validate Kenyan phone number format"""
        if not value:
            return
        
        # Remove any spaces, dashes, or plus signs
        clean_value = re.sub(r'[\s\-\+]', '', str(value))
        
        # Check for valid Kenyan phone number patterns
        patterns = [
            r'^254[17]\d{8}$',  # +254 7xx xxx xxx or +254 1xx xxx xxx
            r'^07\d{8}$',       # 07xx xxx xxx
            r'^01\d{8}$',       # 01xx xxx xxx
        ]
        
        if not any(re.match(pattern, clean_value) for pattern in patterns):
            raise ValidationError(
                'Enter a valid Kenyan phone number (e.g., +254712345678 or 0712345678)',
                code='invalid_phone_number'
            )
    
    @staticmethod
    def validate_nssf_number(value):
        """Validate NSSF number format"""
        if not value:
            return
        
        # NSSF numbers are typically 6-10 digits
        clean_value = re.sub(r'[\s\-]', '', str(value))
        
        if not clean_value.isdigit() or len(clean_value) < 6 or len(clean_value) > 10:
            raise ValidationError(
                'NSSF number must be 6-10 digits',
                code='invalid_nssf_number'
            )
    
    @staticmethod
    def validate_nhif_number(value):
        """Validate NHIF number format"""
        if not value:
            return
        
        # NHIF numbers are typically 8-10 digits
        clean_value = re.sub(r'[\s\-]', '', str(value))
        
        if not clean_value.isdigit() or len(clean_value) < 8 or len(clean_value) > 10:
            raise ValidationError(
                'NHIF number must be 8-10 digits',
                code='invalid_nhif_number'
            )


class BusinessValidators:
    """Validators for business logic and rules"""
    
    @staticmethod
    def validate_employee_id(value):
        """Validate employee ID format"""
        if not value:
            return
        
        # Employee ID should be alphanumeric, 3-20 characters
        pattern = r'^[A-Z0-9]{3,20}$'
        if not re.match(pattern, value.upper()):
            raise ValidationError(
                'Employee ID must be 3-20 alphanumeric characters',
                code='invalid_employee_id'
            )
    
    @staticmethod
    def validate_salary_amount(value):
        """Validate salary amount"""
        if value is None:
            return
        
        if value < 0:
            raise ValidationError(
                'Salary amount cannot be negative',
                code='negative_salary'
            )
        
        if value > Decimal('10000000'):  # 10 million KSH
            raise ValidationError(
                'Salary amount seems unreasonably high',
                code='excessive_salary'
            )
    
    @staticmethod
    def validate_working_hours(value):
        """Validate working hours per day"""
        if value is None:
            return
        
        if value < 0:
            raise ValidationError(
                'Working hours cannot be negative',
                code='negative_hours'
            )
        
        if value > 24:
            raise ValidationError(
                'Working hours cannot exceed 24 hours per day',
                code='excessive_hours'
            )
    
    @staticmethod
    def validate_overtime_hours(value):
        """Validate overtime hours"""
        if value is None:
            return
        
        if value < 0:
            raise ValidationError(
                'Overtime hours cannot be negative',
                code='negative_overtime'
            )
        
        if value > 12:
            raise ValidationError(
                'Overtime hours cannot exceed 12 hours per day',
                code='excessive_overtime'
            )
    
    @staticmethod
    def validate_leave_days(value):
        """Validate leave days"""
        if value is None:
            return
        
        if value < 0:
            raise ValidationError(
                'Leave days cannot be negative',
                code='negative_leave_days'
            )
        
        if value > 365:
            raise ValidationError(
                'Leave days cannot exceed 365 days',
                code='excessive_leave_days'
            )
    
    @staticmethod
    def validate_percentage(value):
        """Validate percentage values (0-100)"""
        if value is None:
            return
        
        if value < 0 or value > 100:
            raise ValidationError(
                'Percentage must be between 0 and 100',
                code='invalid_percentage'
            )
    
    @staticmethod
    def validate_future_date(value):
        """Validate that date is in the future"""
        if value is None:
            return
        
        if value <= timezone.now().date():
            raise ValidationError(
                'Date must be in the future',
                code='past_date'
            )
    
    @staticmethod
    def validate_past_date(value):
        """Validate that date is in the past"""
        if value is None:
            return
        
        if value >= timezone.now().date():
            raise ValidationError(
                'Date must be in the past',
                code='future_date'
            )
    
    @staticmethod
    def validate_date_range(start_date, end_date):
        """Validate date range"""
        if not start_date or not end_date:
            return
        
        if start_date > end_date:
            raise ValidationError(
                'Start date cannot be after end date',
                code='invalid_date_range'
            )
    
    @staticmethod
    def validate_age(birth_date):
        """Validate age for employment"""
        if not birth_date:
            return
        
        today = timezone.now().date()
        age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
        
        if age < 18:
            raise ValidationError(
                'Employee must be at least 18 years old',
                code='underage_employee'
            )
        
        if age > 70:
            raise ValidationError(
                'Employee age seems unreasonable',
                code='excessive_age'
            )


class SecurityValidators:
    """Validators for security and data protection"""
    
    @staticmethod
    def validate_strong_password(password):
        """Validate password strength"""
        if not password:
            return
        
        # Use Django's built-in password validation
        try:
            validate_password(password)
        except ValidationError as e:
            raise ValidationError(e.messages, code='weak_password')
        
        # Additional custom rules
        if len(password) < 8:
            raise ValidationError(
                'Password must be at least 8 characters long',
                code='password_too_short'
            )
        
        if not re.search(r'[A-Z]', password):
            raise ValidationError(
                'Password must contain at least one uppercase letter',
                code='password_no_uppercase'
            )
        
        if not re.search(r'[a-z]', password):
            raise ValidationError(
                'Password must contain at least one lowercase letter',
                code='password_no_lowercase'
            )
        
        if not re.search(r'\d', password):
            raise ValidationError(
                'Password must contain at least one digit',
                code='password_no_digit'
            )
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise ValidationError(
                'Password must contain at least one special character',
                code='password_no_special'
            )
    
    @staticmethod
    def validate_file_size(file, max_size_mb=10):
        """Validate uploaded file size"""
        if not file:
            return
        
        max_size = max_size_mb * 1024 * 1024  # Convert to bytes
        if file.size > max_size:
            raise ValidationError(
                f'File size cannot exceed {max_size_mb}MB',
                code='file_too_large'
            )
    
    @staticmethod
    def validate_file_extension(file, allowed_extensions):
        """Validate file extension"""
        if not file:
            return
        
        import os
        ext = os.path.splitext(file.name)[1].lower()
        if ext not in allowed_extensions:
            raise ValidationError(
                f'File extension {ext} not allowed. Allowed: {", ".join(allowed_extensions)}',
                code='invalid_file_extension'
            )


class DataIntegrityValidators:
    """Validators for data integrity and consistency"""
    
    @staticmethod
    def validate_unique_employee_id(employee_id, exclude_user_id=None):
        """Validate unique employee ID"""
        from .models.auth import User
        
        queryset = User.objects.filter(employee_id=employee_id)
        if exclude_user_id:
            queryset = queryset.exclude(id=exclude_user_id)
        
        if queryset.exists():
            raise ValidationError(
                'Employee ID already exists',
                code='duplicate_employee_id'
            )
    
    @staticmethod
    def validate_unique_email(email, exclude_user_id=None):
        """Validate unique email"""
        from .models.auth import User
        
        queryset = User.objects.filter(email=email)
        if exclude_user_id:
            queryset = queryset.exclude(id=exclude_user_id)
        
        if queryset.exists():
            raise ValidationError(
                'Email address already exists',
                code='duplicate_email'
            )
    
    @staticmethod
    def validate_leave_balance(employee, leave_type, days_requested):
        """Validate leave balance availability"""
        from .models.leave import LeaveBalance
        
        try:
            balance = LeaveBalance.objects.get(
                employee=employee,
                leave_type=leave_type,
                year=timezone.now().year
            )
            
            if balance.remaining_days < days_requested:
                raise ValidationError(
                    f'Insufficient leave balance. Available: {balance.remaining_days} days',
                    code='insufficient_leave_balance'
                )
        except LeaveBalance.DoesNotExist:
            raise ValidationError(
                'No leave balance found for this leave type',
                code='no_leave_balance'
            )
    
    @staticmethod
    def validate_overtime_budget(employee, department, hours_requested, amount_requested):
        """Validate overtime budget availability"""
        from .models.overtime import OvertimeBudget
        
        current_month = timezone.now().date().replace(day=1)
        
        # Check employee budget
        try:
            budget = OvertimeBudget.objects.get(
                employee=employee,
                budget_year=current_month.year,
                budget_month=current_month.month,
                is_active=True
            )
            
            if budget.remaining_hours < hours_requested:
                raise ValidationError(
                    f'Insufficient overtime budget. Available: {budget.remaining_hours} hours',
                    code='insufficient_overtime_budget'
                )
        except OvertimeBudget.DoesNotExist:
            # Check department budget
            try:
                dept_budget = OvertimeBudget.objects.get(
                    department=department,
                    budget_year=current_month.year,
                    budget_month=current_month.month,
                    is_active=True
                )
                
                if dept_budget.remaining_hours < hours_requested:
                    raise ValidationError(
                        f'Insufficient department overtime budget. Available: {dept_budget.remaining_hours} hours',
                        code='insufficient_department_overtime_budget'
                    )
            except OvertimeBudget.DoesNotExist:
                # No budget restrictions
                pass


# Regex validators for common patterns
kenyan_phone_validator = RegexValidator(
    regex=r'^(\+254|0)[17]\d{8}$',
    message='Enter a valid Kenyan phone number',
    code='invalid_phone'
)

employee_id_validator = RegexValidator(
    regex=r'^[A-Z0-9]{3,20}$',
    message='Employee ID must be 3-20 alphanumeric characters',
    code='invalid_employee_id'
)

kra_pin_validator = RegexValidator(
    regex=r'^[A-Z]\d{9}[A-Z]$',
    message='KRA PIN must be in format A123456789Z',
    code='invalid_kra_pin'
)

# Custom field validators
def validate_positive_decimal(value):
    """Validate positive decimal values"""
    if value is not None and value < 0:
        raise ValidationError('Value must be positive', code='negative_value')

def validate_working_day(value):
    """Validate working day (Monday-Friday)"""
    if value and value.weekday() >= 5:  # Saturday = 5, Sunday = 6
        raise ValidationError('Date must be a working day (Monday-Friday)', code='non_working_day')

def validate_business_hours(value):
    """Validate business hours (8 AM - 6 PM)"""
    if value:
        if value.hour < 8 or value.hour >= 18:
            raise ValidationError('Time must be within business hours (8 AM - 6 PM)', code='non_business_hours')
