"""
Comprehensive Error Management System for WorkFlo Backend
Custom exceptions, error handlers, and error response formatting
"""

import logging
from rest_framework import status
from rest_framework.views import exception_handler
from rest_framework.response import Response
from django.core.exceptions import ValidationError as DjangoValidationError
from django.db import IntegrityError
from django.http import Http404
from django.core.exceptions import PermissionDenied
from rest_framework.exceptions import (
    ValidationError, PermissionDenied as DRFPermissionDenied,
    AuthenticationFailed, NotAuthenticated, NotFound,
    MethodNotAllowed, ParseError, UnsupportedMediaType,
    Throttled, APIException
)

logger = logging.getLogger(__name__)


class WorkFloException(APIException):
    """Base exception class for WorkFlo application"""
    status_code = status.HTTP_400_BAD_REQUEST
    default_detail = 'A business logic error occurred.'
    default_code = 'business_error'


class BusinessLogicError(WorkFloException):
    """Exception for business logic violations"""
    default_detail = 'Business rule violation.'
    default_code = 'business_logic_error'


class InsufficientBalanceError(BusinessLogicError):
    """Exception for insufficient balance (leave, overtime budget, etc.)"""
    default_detail = 'Insufficient balance for this operation.'
    default_code = 'insufficient_balance'


class InvalidWorkflowStateError(BusinessLogicError):
    """Exception for invalid workflow state transitions"""
    default_detail = 'Invalid workflow state transition.'
    default_code = 'invalid_workflow_state'


class DuplicateRecordError(WorkFloException):
    """Exception for duplicate record attempts"""
    status_code = status.HTTP_409_CONFLICT
    default_detail = 'Record already exists.'
    default_code = 'duplicate_record'


class RecordNotFoundError(WorkFloException):
    """Exception for record not found"""
    status_code = status.HTTP_404_NOT_FOUND
    default_detail = 'Record not found.'
    default_code = 'record_not_found'


class InvalidDateRangeError(BusinessLogicError):
    """Exception for invalid date ranges"""
    default_detail = 'Invalid date range specified.'
    default_code = 'invalid_date_range'


class PayrollProcessingError(BusinessLogicError):
    """Exception for payroll processing errors"""
    default_detail = 'Error processing payroll.'
    default_code = 'payroll_processing_error'


class AttendanceValidationError(BusinessLogicError):
    """Exception for attendance validation errors"""
    default_detail = 'Invalid attendance data.'
    default_code = 'attendance_validation_error'


class LeaveApplicationError(BusinessLogicError):
    """Exception for leave application errors"""
    default_detail = 'Error processing leave application.'
    default_code = 'leave_application_error'


class OvertimeValidationError(BusinessLogicError):
    """Exception for overtime validation errors"""
    default_detail = 'Invalid overtime request.'
    default_code = 'overtime_validation_error'


class TrainingEnrollmentError(BusinessLogicError):
    """Exception for training enrollment errors"""
    default_detail = 'Error enrolling in training.'
    default_code = 'training_enrollment_error'


class DocumentAccessError(WorkFloException):
    """Exception for document access errors"""
    status_code = status.HTTP_403_FORBIDDEN
    default_detail = 'Access denied to document.'
    default_code = 'document_access_error'


class SystemMaintenanceError(WorkFloException):
    """Exception for system maintenance mode"""
    status_code = status.HTTP_503_SERVICE_UNAVAILABLE
    default_detail = 'System is under maintenance.'
    default_code = 'system_maintenance'


class ExternalServiceError(WorkFloException):
    """Exception for external service failures"""
    status_code = status.HTTP_502_BAD_GATEWAY
    default_detail = 'External service unavailable.'
    default_code = 'external_service_error'


class RateLimitExceededError(WorkFloException):
    """Exception for rate limit exceeded"""
    status_code = status.HTTP_429_TOO_MANY_REQUESTS
    default_detail = 'Rate limit exceeded.'
    default_code = 'rate_limit_exceeded'


def custom_exception_handler(exc, context):
    """
    Custom exception handler for WorkFlo backend
    Provides consistent error response format and logging
    """
    
    # Get the standard error response
    response = exception_handler(exc, context)
    
    # Get request information for logging
    request = context.get('request')
    view = context.get('view')
    
    # Log the exception
    if response is not None:
        log_exception(exc, request, view, response.status_code)
    
    # Handle specific exception types
    if isinstance(exc, DjangoValidationError):
        response = handle_django_validation_error(exc)
    elif isinstance(exc, IntegrityError):
        response = handle_integrity_error(exc)
    elif isinstance(exc, Http404):
        response = handle_not_found_error(exc)
    elif isinstance(exc, PermissionDenied):
        response = handle_permission_denied_error(exc)
    elif isinstance(exc, WorkFloException):
        response = handle_workflo_exception(exc)
    elif response is not None:
        # Customize the response format for DRF exceptions
        response = format_error_response(exc, response)
    else:
        # Handle unexpected exceptions
        response = handle_unexpected_error(exc)
        log_exception(exc, request, view, 500, is_unexpected=True)
    
    return response


def log_exception(exc, request, view, status_code, is_unexpected=False):
    """Log exception details"""
    
    user = getattr(request, 'user', None)
    user_info = f"User: {user.email if user and user.is_authenticated else 'Anonymous'}"
    
    view_info = f"View: {view.__class__.__name__}" if view else "View: Unknown"
    
    method = getattr(request, 'method', 'Unknown')
    path = getattr(request, 'path', 'Unknown')
    
    log_level = logging.ERROR if is_unexpected or status_code >= 500 else logging.WARNING
    
    logger.log(
        log_level,
        f"Exception in {method} {path} | {view_info} | {user_info} | "
        f"Status: {status_code} | Exception: {exc.__class__.__name__}: {str(exc)}"
    )


def handle_django_validation_error(exc):
    """Handle Django ValidationError"""
    
    if hasattr(exc, 'error_dict'):
        # Field-specific errors
        errors = {}
        for field, error_list in exc.error_dict.items():
            errors[field] = [str(error) for error in error_list]
    elif hasattr(exc, 'error_list'):
        # Non-field errors
        errors = {'non_field_errors': [str(error) for error in exc.error_list]}
    else:
        # Single error message
        errors = {'non_field_errors': [str(exc)]}
    
    return Response({
        'error': 'Validation failed',
        'error_code': 'validation_error',
        'details': errors,
        'timestamp': get_timestamp()
    }, status=status.HTTP_400_BAD_REQUEST)


def handle_integrity_error(exc):
    """Handle database integrity errors"""
    
    error_message = str(exc)
    
    # Check for common integrity constraint violations
    if 'UNIQUE constraint failed' in error_message:
        error_code = 'duplicate_record'
        user_message = 'A record with this information already exists'
    elif 'FOREIGN KEY constraint failed' in error_message:
        error_code = 'invalid_reference'
        user_message = 'Referenced record does not exist'
    elif 'NOT NULL constraint failed' in error_message:
        error_code = 'required_field_missing'
        user_message = 'Required field is missing'
    else:
        error_code = 'database_error'
        user_message = 'Database constraint violation'
    
    return Response({
        'error': user_message,
        'error_code': error_code,
        'details': {'database_error': error_message},
        'timestamp': get_timestamp()
    }, status=status.HTTP_400_BAD_REQUEST)


def handle_not_found_error(exc):
    """Handle 404 Not Found errors"""
    
    return Response({
        'error': 'Resource not found',
        'error_code': 'not_found',
        'details': {'message': str(exc) if str(exc) else 'The requested resource was not found'},
        'timestamp': get_timestamp()
    }, status=status.HTTP_404_NOT_FOUND)


def handle_permission_denied_error(exc):
    """Handle permission denied errors"""
    
    return Response({
        'error': 'Permission denied',
        'error_code': 'permission_denied',
        'details': {'message': str(exc) if str(exc) else 'You do not have permission to perform this action'},
        'timestamp': get_timestamp()
    }, status=status.HTTP_403_FORBIDDEN)


def handle_workflo_exception(exc):
    """Handle custom WorkFlo exceptions"""
    
    return Response({
        'error': str(exc.detail),
        'error_code': exc.default_code,
        'details': getattr(exc, 'extra_details', {}),
        'timestamp': get_timestamp()
    }, status=exc.status_code)


def handle_unexpected_error(exc):
    """Handle unexpected errors"""
    
    return Response({
        'error': 'An unexpected error occurred',
        'error_code': 'internal_server_error',
        'details': {'message': 'Please contact system administrator'},
        'timestamp': get_timestamp()
    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def format_error_response(exc, response):
    """Format DRF exception responses consistently"""
    
    # Map DRF exception types to error codes
    error_code_mapping = {
        ValidationError: 'validation_error',
        AuthenticationFailed: 'authentication_failed',
        NotAuthenticated: 'not_authenticated',
        DRFPermissionDenied: 'permission_denied',
        NotFound: 'not_found',
        MethodNotAllowed: 'method_not_allowed',
        ParseError: 'parse_error',
        UnsupportedMediaType: 'unsupported_media_type',
        Throttled: 'throttled',
    }
    
    error_code = error_code_mapping.get(type(exc), 'api_error')
    
    # Get error message
    if hasattr(exc, 'detail'):
        if isinstance(exc.detail, dict):
            error_message = 'Validation failed'
            details = exc.detail
        elif isinstance(exc.detail, list):
            error_message = str(exc.detail[0]) if exc.detail else 'An error occurred'
            details = {'errors': exc.detail}
        else:
            error_message = str(exc.detail)
            details = {}
    else:
        error_message = 'An error occurred'
        details = {}
    
    # Format response
    formatted_response = {
        'error': error_message,
        'error_code': error_code,
        'details': details,
        'timestamp': get_timestamp()
    }
    
    response.data = formatted_response
    return response


def get_timestamp():
    """Get current timestamp in ISO format"""
    from django.utils import timezone
    return timezone.now().isoformat()


class ErrorMessages:
    """Centralized error messages for consistency"""
    
    # Authentication errors
    INVALID_CREDENTIALS = "Invalid email or password"
    ACCOUNT_DISABLED = "Your account has been disabled"
    TOKEN_EXPIRED = "Authentication token has expired"
    TOKEN_INVALID = "Invalid authentication token"
    
    # Permission errors
    INSUFFICIENT_PERMISSIONS = "You do not have permission to perform this action"
    ROLE_REQUIRED = "This action requires {role} role"
    DEPARTMENT_ACCESS_DENIED = "You can only access data from your department"
    
    # Validation errors
    REQUIRED_FIELD = "This field is required"
    INVALID_FORMAT = "Invalid format for {field}"
    VALUE_TOO_LARGE = "Value is too large for {field}"
    VALUE_TOO_SMALL = "Value is too small for {field}"
    
    # Business logic errors
    INSUFFICIENT_LEAVE_BALANCE = "Insufficient leave balance. Available: {available} days"
    INVALID_DATE_RANGE = "Start date cannot be after end date"
    OVERLAPPING_SCHEDULE = "Schedule conflicts with existing {type}"
    BUDGET_EXCEEDED = "Operation would exceed {budget_type} budget"
    
    # System errors
    SYSTEM_MAINTENANCE = "System is currently under maintenance"
    EXTERNAL_SERVICE_UNAVAILABLE = "External service is temporarily unavailable"
    RATE_LIMIT_EXCEEDED = "Too many requests. Please try again later"
    
    @classmethod
    def format_message(cls, message_template, **kwargs):
        """Format message with parameters"""
        return message_template.format(**kwargs)


class ValidationErrorCollector:
    """Utility class to collect multiple validation errors"""
    
    def __init__(self):
        self.errors = {}
    
    def add_error(self, field, message, code=None):
        """Add a validation error for a field"""
        if field not in self.errors:
            self.errors[field] = []
        
        error_dict = {'message': message}
        if code:
            error_dict['code'] = code
        
        self.errors[field].append(error_dict)
    
    def add_non_field_error(self, message, code=None):
        """Add a non-field validation error"""
        self.add_error('non_field_errors', message, code)
    
    def has_errors(self):
        """Check if there are any errors"""
        return bool(self.errors)
    
    def raise_if_errors(self):
        """Raise ValidationError if there are any errors"""
        if self.has_errors():
            raise ValidationError(self.errors)
