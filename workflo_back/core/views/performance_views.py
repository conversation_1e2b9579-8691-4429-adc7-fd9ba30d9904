# Performance Management Views
# Placeholder for performance views with CRUD operations

from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from ..models.performance import PerformanceReviewTemplate, PerformanceReview, PerformanceGoal


class PerformanceReviewTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for PerformanceReviewTemplate management with CRUD operations
    """
    queryset = PerformanceReviewTemplate.objects.filter(is_active=True)
    permission_classes = [IsAuthenticated]


class PerformanceReviewViewSet(viewsets.ModelViewSet):
    """
    ViewSet for PerformanceReview management with CRUD operations
    """
    queryset = PerformanceReview.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter based on user role and permissions"""
        user = self.request.user
        queryset = self.queryset.select_related('employee', 'reviewer', 'template')
        
        # Business logic: Filter based on user role
        if user.role == 'employee':
            # Employees can see reviews where they are the employee or reviewer
            queryset = queryset.filter(employee=user)
        elif user.role == 'supervisor':
            # Supervisors can see reviews for their department
            if hasattr(user, 'employee_profile'):
                department = user.employee_profile.department
                queryset = queryset.filter(employee__employee_profile__department=department)
        # Admin, HR can see all performance reviews
        
        return queryset


class PerformanceGoalViewSet(viewsets.ModelViewSet):
    """
    ViewSet for PerformanceGoal management with CRUD operations
    """
    queryset = PerformanceGoal.objects.all()
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter based on user role and permissions"""
        user = self.request.user
        queryset = self.queryset.select_related('employee', 'review')
        
        # Business logic: Filter based on user role
        if user.role == 'employee':
            # Employees can see their own goals
            queryset = queryset.filter(employee=user)
        elif user.role == 'supervisor':
            # Supervisors can see goals for their department
            if hasattr(user, 'employee_profile'):
                department = user.employee_profile.department
                queryset = queryset.filter(employee__employee_profile__department=department)
        # Admin, HR can see all performance goals
        
        return queryset
