from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import Is<PERSON><PERSON><PERSON>icated, AllowAny
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from django.contrib.auth import authenticate
from django.utils import timezone
from ..models.auth import User, UserSession, PasswordResetToken
from ..serializers.auth_serializers import (
    UserSerializer, UserCreateSerializer, UserUpdateSerializer,
    CustomTokenObtainPairSerializer, PasswordResetSerializer
)


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom JWT token view with additional user information
    """
    serializer_class = CustomTokenObtainPairSerializer
    permission_classes = [AllowAny]
    
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        
        if response.status_code == 200:
            # Update last login
            email = request.data.get('email')
            if email:
                try:
                    user = User.objects.get(email=email)
                    user.last_login = timezone.now()
                    user.save()
                    
                    # Create user session record
                    UserSession.objects.create(
                        user=user,
                        access_token=response.data['access'],
                        refresh_token=response.data['refresh'],
                        expires_at=timezone.now() + timezone.timedelta(hours=1),
                        ip_address=request.META.get('REMOTE_ADDR'),
                        user_agent=request.META.get('HTTP_USER_AGENT', '')
                    )
                except User.DoesNotExist:
                    pass
        
        return response


class UserViewSet(viewsets.ModelViewSet):
    """
    ViewSet for User management with CRUD operations
    Business logic is handled in views, not models
    """
    queryset = User.objects.filter(is_deleted=False)
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'create':
            return UserCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        return UserSerializer
    
    def get_permissions(self):
        """Set permissions based on action"""
        if self.action == 'create':
            # Allow user creation for registration
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def perform_create(self, serializer):
        """Handle user creation with business logic"""
        # Generate employee ID if not provided
        if not serializer.validated_data.get('employee_id'):
            # Business logic: Generate employee ID based on company format
            last_user = User.objects.filter(employee_id__startswith='EMP').order_by('-id').first()
            if last_user and last_user.employee_id:
                try:
                    last_number = int(last_user.employee_id.replace('EMP', ''))
                    new_number = last_number + 1
                except ValueError:
                    new_number = 1
            else:
                new_number = 1
            
            serializer.validated_data['employee_id'] = f'EMP{new_number:04d}'
        
        # Set created_by
        if self.request.user.is_authenticated:
            serializer.validated_data['created_by'] = self.request.user
        
        serializer.save()
    
    def perform_update(self, serializer):
        """Handle user update with business logic"""
        serializer.validated_data['updated_by'] = self.request.user
        serializer.save()
    
    def destroy(self, request, *args, **kwargs):
        """Soft delete user instead of hard delete"""
        instance = self.get_object()
        
        # Business logic: Check if user can be deleted
        if instance.role == 'admin' and User.objects.filter(role='admin', is_deleted=False).count() <= 1:
            return Response(
                {'error': 'Cannot delete the last admin user'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Perform soft delete
        instance.soft_delete(deleted_by=request.user)
        
        return Response(status=status.HTTP_204_NO_CONTENT)
    
    @action(detail=False, methods=['get'])
    def me(self, request):
        """Get current user information"""
        serializer = self.get_serializer(request.user)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def restore(self, request, pk=None):
        """Restore a soft-deleted user"""
        try:
            user = User.all_objects.get(pk=pk, is_deleted=True)
            user.restore()
            serializer = self.get_serializer(user)
            return Response(serializer.data)
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found or not deleted'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'])
    def by_role(self, request):
        """Get users by role"""
        role = request.query_params.get('role')
        if not role:
            return Response(
                {'error': 'Role parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        users = self.get_queryset().filter(role=role)
        serializer = self.get_serializer(users, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_department(self, request):
        """Get users by department"""
        department_id = request.query_params.get('department_id')
        if not department_id:
            return Response(
                {'error': 'Department ID parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        users = self.get_queryset().filter(employee_profile__department_id=department_id)
        serializer = self.get_serializer(users, many=True)
        return Response(serializer.data)


class PasswordResetViewSet(viewsets.ViewSet):
    """
    ViewSet for password reset functionality
    """
    permission_classes = [AllowAny]
    
    @action(detail=False, methods=['post'])
    def request_reset(self, request):
        """Request password reset"""
        serializer = PasswordResetSerializer(data=request.data)
        if serializer.is_valid():
            # Business logic for password reset
            email = serializer.validated_data['email']
            try:
                user = User.objects.get(email=email, is_deleted=False)
                
                # Generate reset token
                import secrets
                token = secrets.token_urlsafe(32)
                
                # Create password reset token
                PasswordResetToken.objects.create(
                    user=user,
                    token=token,
                    expires_at=timezone.now() + timezone.timedelta(hours=24)
                )
                
                # TODO: Send email with reset link
                # This would typically integrate with an email service
                
                return Response({'message': 'Password reset email sent'})
            except User.DoesNotExist:
                # Don't reveal if email exists for security
                return Response({'message': 'Password reset email sent'})
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def reset_password(self, request):
        """Reset password with token"""
        token = request.data.get('token')
        new_password = request.data.get('new_password')
        
        if not token or not new_password:
            return Response(
                {'error': 'Token and new password are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            reset_token = PasswordResetToken.objects.get(
                token=token,
                used=False,
                expires_at__gt=timezone.now()
            )
            
            # Reset password
            user = reset_token.user
            user.set_password(new_password)
            user.save()
            
            # Mark token as used
            reset_token.used = True
            reset_token.save()
            
            return Response({'message': 'Password reset successfully'})
            
        except PasswordResetToken.DoesNotExist:
            return Response(
                {'error': 'Invalid or expired token'},
                status=status.HTTP_400_BAD_REQUEST
            )
