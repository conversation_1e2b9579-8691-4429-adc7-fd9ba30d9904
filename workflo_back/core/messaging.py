"""
Comprehensive Messaging System for WorkFlo Backend
Success messages, notifications, and user feedback management
"""

import logging
from enum import Enum
from django.utils import timezone
from django.template.loader import render_to_string
from django.core.mail import send_mail
from django.conf import settings
from rest_framework.response import Response
from rest_framework import status

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Message types for different scenarios"""
    SUCCESS = "success"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


class MessageCategory(Enum):
    """Message categories for organization"""
    AUTHENTICATION = "authentication"
    EMPLOYEE = "employee"
    ATTENDANCE = "attendance"
    LEAVE = "leave"
    PAYROLL = "payroll"
    TRAINING = "training"
    PERFORMANCE = "performance"
    SYSTEM = "system"


class SuccessMessages:
    """Centralized success messages"""
    
    # Authentication messages
    LOGIN_SUCCESS = "Successfully logged in"
    LOGOUT_SUCCESS = "Successfully logged out"
    PASSWORD_CHANGED = "Password changed successfully"
    PASSWORD_RESET_SENT = "Password reset instructions sent to your email"
    ACCOUNT_ACTIVATED = "Account activated successfully"
    
    # Employee management messages
    EMPLOYEE_CREATED = "Employee profile created successfully"
    EMPLOYEE_UPDATED = "Employee profile updated successfully"
    EMPLOYEE_DELETED = "Employee profile deleted successfully"
    PROFILE_COMPLETED = "Profile setup completed successfully"
    
    # Attendance messages
    ATTENDANCE_RECORDED = "Attendance recorded successfully"
    ATTENDANCE_UPDATED = "Attendance updated successfully"
    CHECK_IN_SUCCESS = "Check-in recorded at {time}"
    CHECK_OUT_SUCCESS = "Check-out recorded at {time}"
    
    # Leave management messages
    LEAVE_APPLIED = "Leave application submitted successfully"
    LEAVE_APPROVED = "Leave application approved"
    LEAVE_REJECTED = "Leave application rejected"
    LEAVE_CANCELLED = "Leave application cancelled"
    LEAVE_BALANCE_UPDATED = "Leave balance updated successfully"
    
    # Overtime messages
    OVERTIME_REQUESTED = "Overtime request submitted successfully"
    OVERTIME_APPROVED = "Overtime request approved"
    OVERTIME_REJECTED = "Overtime request rejected"
    OVERTIME_RECORDED = "Overtime hours recorded successfully"
    
    # Payroll messages
    PAYROLL_GENERATED = "Payroll generated successfully for {period}"
    PAYROLL_PROCESSED = "Payroll processed successfully"
    SALARY_UPDATED = "Salary information updated successfully"
    PAYSLIP_GENERATED = "Payslip generated successfully"
    
    # Training messages
    TRAINING_ENROLLED = "Successfully enrolled in {course}"
    TRAINING_COMPLETED = "Training completed successfully"
    TRAINING_CANCELLED = "Training enrollment cancelled"
    CERTIFICATE_ISSUED = "Certificate issued successfully"
    
    # Performance messages
    REVIEW_SUBMITTED = "Performance review submitted successfully"
    REVIEW_APPROVED = "Performance review approved"
    GOAL_CREATED = "Performance goal created successfully"
    GOAL_UPDATED = "Performance goal updated successfully"
    
    # System messages
    DATA_EXPORTED = "Data exported successfully"
    BACKUP_CREATED = "System backup created successfully"
    SETTINGS_UPDATED = "Settings updated successfully"
    
    @classmethod
    def format_message(cls, message_template, **kwargs):
        """Format message with parameters"""
        return message_template.format(**kwargs)


class InfoMessages:
    """Informational messages for users"""
    
    # General info
    PROFILE_INCOMPLETE = "Please complete your profile information"
    PENDING_APPROVALS = "You have {count} pending approvals"
    UPCOMING_DEADLINES = "You have {count} upcoming deadlines"
    
    # Leave info
    LEAVE_BALANCE_LOW = "Your {leave_type} balance is running low: {balance} days remaining"
    LEAVE_YEAR_END = "Unused leave days will expire on {date}"
    
    # Training info
    MANDATORY_TRAINING = "You have {count} mandatory training courses to complete"
    TRAINING_DEADLINE = "Training '{course}' deadline is approaching: {date}"
    
    # Payroll info
    PAYROLL_PROCESSING = "Payroll is being processed for {period}"
    PAYSLIP_AVAILABLE = "Your payslip for {period} is now available"
    
    # System info
    MAINTENANCE_SCHEDULED = "System maintenance scheduled for {date}"
    NEW_FEATURES = "New features have been added to the system"


class WarningMessages:
    """Warning messages for potential issues"""
    
    # Authentication warnings
    PASSWORD_EXPIRING = "Your password will expire in {days} days"
    MULTIPLE_LOGIN_ATTEMPTS = "Multiple failed login attempts detected"
    
    # Attendance warnings
    LATE_CHECK_IN = "Late check-in recorded. Please contact your supervisor"
    MISSING_CHECK_OUT = "You forgot to check out yesterday"
    ATTENDANCE_PATTERN = "Irregular attendance pattern detected"
    
    # Leave warnings
    LEAVE_QUOTA_EXCEEDED = "Leave request exceeds available quota"
    OVERLAPPING_LEAVE = "Leave request overlaps with existing leave"
    HOLIDAY_CONFLICT = "Leave request conflicts with company holiday"
    
    # Overtime warnings
    OVERTIME_LIMIT_APPROACHING = "Approaching overtime limit for this month"
    OVERTIME_BUDGET_LOW = "Department overtime budget is running low"
    
    # Performance warnings
    REVIEW_OVERDUE = "Performance review is overdue"
    GOAL_DEADLINE_APPROACHING = "Performance goal deadline approaching"
    
    # System warnings
    DISK_SPACE_LOW = "System disk space is running low"
    HIGH_ERROR_RATE = "High error rate detected in the system"


class NotificationService:
    """Service for sending notifications to users"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def send_notification(self, user, title, message, category=MessageCategory.SYSTEM, 
                         message_type=MessageType.INFO, send_email=False):
        """Send notification to user"""
        
        try:
            # Create notification record
            from .models.notifications import Notification
            
            notification = Notification.objects.create(
                recipient=user,
                title=title,
                message=message,
                category=category.value,
                message_type=message_type.value,
                created_at=timezone.now()
            )
            
            # Send email if requested
            if send_email and user.email:
                self._send_email_notification(user, title, message)
            
            self.logger.info(f"Notification sent to {user.email}: {title}")
            return notification
            
        except Exception as e:
            self.logger.error(f"Failed to send notification to {user.email}: {str(e)}")
            return None
    
    def _send_email_notification(self, user, title, message):
        """Send email notification"""
        
        try:
            subject = f"WorkFlo Notification: {title}"
            
            # Use template if available, otherwise plain text
            try:
                html_message = render_to_string('emails/notification.html', {
                    'user': user,
                    'title': title,
                    'message': message,
                    'timestamp': timezone.now()
                })
            except:
                html_message = None
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message,
                fail_silently=False
            )
            
        except Exception as e:
            self.logger.error(f"Failed to send email to {user.email}: {str(e)}")
    
    def send_bulk_notification(self, users, title, message, category=MessageCategory.SYSTEM,
                              message_type=MessageType.INFO, send_email=False):
        """Send notification to multiple users"""
        
        notifications = []
        for user in users:
            notification = self.send_notification(
                user, title, message, category, message_type, send_email
            )
            if notification:
                notifications.append(notification)
        
        return notifications
    
    def send_role_notification(self, role, title, message, category=MessageCategory.SYSTEM,
                              message_type=MessageType.INFO, send_email=False):
        """Send notification to all users with specific role"""
        
        from .models.auth import User
        users = User.objects.filter(role=role, is_active=True)
        return self.send_bulk_notification(users, title, message, category, message_type, send_email)
    
    def send_department_notification(self, department, title, message, 
                                   category=MessageCategory.SYSTEM,
                                   message_type=MessageType.INFO, send_email=False):
        """Send notification to all users in a department"""
        
        from .models.auth import User
        users = User.objects.filter(
            employee_profile__department=department,
            is_active=True
        )
        return self.send_bulk_notification(users, title, message, category, message_type, send_email)


class ResponseFormatter:
    """Utility class for formatting API responses consistently"""
    
    @staticmethod
    def success_response(data=None, message=None, status_code=status.HTTP_200_OK):
        """Format success response"""
        
        response_data = {
            'success': True,
            'timestamp': timezone.now().isoformat()
        }
        
        if message:
            response_data['message'] = message
        
        if data is not None:
            response_data['data'] = data
        
        return Response(response_data, status=status_code)
    
    @staticmethod
    def created_response(data=None, message=None):
        """Format created response"""
        return ResponseFormatter.success_response(
            data=data,
            message=message or "Resource created successfully",
            status_code=status.HTTP_201_CREATED
        )
    
    @staticmethod
    def updated_response(data=None, message=None):
        """Format updated response"""
        return ResponseFormatter.success_response(
            data=data,
            message=message or "Resource updated successfully"
        )
    
    @staticmethod
    def deleted_response(message=None):
        """Format deleted response"""
        return ResponseFormatter.success_response(
            message=message or "Resource deleted successfully",
            status_code=status.HTTP_204_NO_CONTENT
        )
    
    @staticmethod
    def error_response(error_message, error_code=None, details=None, 
                      status_code=status.HTTP_400_BAD_REQUEST):
        """Format error response"""
        
        response_data = {
            'success': False,
            'error': error_message,
            'timestamp': timezone.now().isoformat()
        }
        
        if error_code:
            response_data['error_code'] = error_code
        
        if details:
            response_data['details'] = details
        
        return Response(response_data, status=status_code)
    
    @staticmethod
    def validation_error_response(errors):
        """Format validation error response"""
        
        return ResponseFormatter.error_response(
            error_message="Validation failed",
            error_code="validation_error",
            details=errors,
            status_code=status.HTTP_400_BAD_REQUEST
        )


class MessageTemplates:
    """Templates for common message scenarios"""
    
    # Email templates
    LEAVE_APPROVAL_REQUEST = """
    Dear {supervisor_name},
    
    {employee_name} has submitted a leave application that requires your approval.
    
    Leave Type: {leave_type}
    Start Date: {start_date}
    End Date: {end_date}
    Days Requested: {days}
    Reason: {reason}
    
    Please review and approve/reject this request in the WorkFlo system.
    
    Best regards,
    WorkFlo System
    """
    
    OVERTIME_APPROVAL_REQUEST = """
    Dear {supervisor_name},
    
    {employee_name} has submitted an overtime request that requires your approval.
    
    Date: {date}
    Start Time: {start_time}
    End Time: {end_time}
    Hours: {hours}
    Reason: {reason}
    
    Please review and approve/reject this request in the WorkFlo system.
    
    Best regards,
    WorkFlo System
    """
    
    PAYROLL_NOTIFICATION = """
    Dear {employee_name},
    
    Your payslip for {period} is now available in the WorkFlo system.
    
    Gross Salary: {gross_salary}
    Net Salary: {net_salary}
    
    Please log in to view the detailed payslip.
    
    Best regards,
    WorkFlo HR Team
    """
    
    TRAINING_REMINDER = """
    Dear {employee_name},
    
    This is a reminder that you have a mandatory training course to complete:
    
    Course: {course_name}
    Deadline: {deadline}
    Duration: {duration} hours
    
    Please complete this training before the deadline.
    
    Best regards,
    WorkFlo Training Team
    """
    
    @classmethod
    def format_template(cls, template, **kwargs):
        """Format template with parameters"""
        return template.format(**kwargs)


# Initialize notification service
notification_service = NotificationService()
