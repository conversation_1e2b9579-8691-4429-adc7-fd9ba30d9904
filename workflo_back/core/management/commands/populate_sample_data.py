from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import date, timedelta
from core.models.auth import User
from core.models.organization import Department
from core.models.employees import EmployeeProfile, SalaryProfile
from core.models.leave import LeaveType, LeaveBalance


class Command(BaseCommand):
    help = 'Populate the database with sample data for testing'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🚀 Starting to populate sample data...'))
        
        # Create departments
        self.create_departments()
        
        # Create leave types
        self.create_leave_types()
        
        # Create sample employees
        self.create_sample_employees()
        
        self.stdout.write(self.style.SUCCESS('✅ Sample data populated successfully!'))

    def create_departments(self):
        """Create sample departments"""
        self.stdout.write('📁 Creating departments...')
        
        departments = [
            {'name': 'Human Resources', 'description': 'HR Department'},
            {'name': 'Information Technology', 'description': 'IT Department'},
            {'name': 'Finance & Accounting', 'description': 'Finance Department'},
            {'name': 'Sales & Marketing', 'description': 'Sales Department'},
            {'name': 'Operations', 'description': 'Operations Department'},
        ]
        
        for dept_data in departments:
            dept, created = Department.objects.get_or_create(
                name=dept_data['name'],
                defaults=dept_data
            )
            if created:
                self.stdout.write(f'  ✅ Created department: {dept.name}')

    def create_leave_types(self):
        """Create sample leave types"""
        self.stdout.write('🏖️ Creating leave types...')
        
        leave_types = [
            {
                'name': 'Annual Leave',
                'description': 'Annual vacation leave',
                'max_days_per_year': 21,
                'carry_forward_allowed': True,
                'max_carry_forward_days': 5,
                'is_paid': True
            },
            {
                'name': 'Sick Leave',
                'description': 'Medical leave',
                'max_days_per_year': 14,
                'carry_forward_allowed': False,
                'is_paid': True
            },
            {
                'name': 'Maternity Leave',
                'description': 'Maternity leave',
                'max_days_per_year': 90,
                'carry_forward_allowed': False,
                'is_paid': True
            },
            {
                'name': 'Paternity Leave',
                'description': 'Paternity leave',
                'max_days_per_year': 14,
                'carry_forward_allowed': False,
                'is_paid': True
            },
            {
                'name': 'Emergency Leave',
                'description': 'Emergency leave',
                'max_days_per_year': 5,
                'carry_forward_allowed': False,
                'is_paid': False
            }
        ]
        
        for leave_data in leave_types:
            leave_type, created = LeaveType.objects.get_or_create(
                name=leave_data['name'],
                defaults=leave_data
            )
            if created:
                self.stdout.write(f'  ✅ Created leave type: {leave_type.name}')

    def create_sample_employees(self):
        """Create sample employees"""
        self.stdout.write('👥 Creating sample employees...')

        # Check if sample data already exists
        if User.objects.filter(role__in=['hr', 'supervisor', 'accountant', 'employee']).count() >= 8:
            self.stdout.write('ℹ️ Sample employees already exist')
            return

        # Get departments
        hr_dept = Department.objects.get(name='Human Resources')
        it_dept = Department.objects.get(name='Information Technology')
        finance_dept = Department.objects.get(name='Finance & Accounting')
        
        # Get additional departments
        sales_dept, _ = Department.objects.get_or_create(
            name='Sales & Marketing',
            defaults={'description': 'Sales Department'}
        )
        operations_dept, _ = Department.objects.get_or_create(
            name='Operations',
            defaults={'description': 'Operations Department'}
        )

        employees = [
            {
                'user_data': {
                    'email': '<EMAIL>',
                    'username': 'hr_manager',
                    'first_name': 'Sarah',
                    'last_name': 'Johnson',
                    'employee_id': 'EMP0002',
                    'role': 'hr'
                },
                'profile_data': {
                    'department': hr_dept,
                    'job_title': 'HR Manager',
                    'hire_date': date.today() - timedelta(days=365),
                    'employment_type': 'full_time',
                    'work_location': 'office'
                },
                'salary_data': {
                    'basic_salary': 120000,
                    'allowances': 20000,
                    'pay_frequency': 'monthly'
                }
            },
            {
                'user_data': {
                    'email': '<EMAIL>',
                    'username': 'supervisor',
                    'first_name': 'Michael',
                    'last_name': 'Chen',
                    'employee_id': 'EMP0003',
                    'role': 'supervisor'
                },
                'profile_data': {
                    'department': it_dept,
                    'job_title': 'IT Team Lead',
                    'hire_date': date.today() - timedelta(days=500),
                    'employment_type': 'full_time',
                    'work_location': 'hybrid'
                },
                'salary_data': {
                    'basic_salary': 150000,
                    'allowances': 25000,
                    'pay_frequency': 'monthly'
                }
            },
            {
                'user_data': {
                    'email': '<EMAIL>',
                    'username': 'accountant',
                    'first_name': 'Grace',
                    'last_name': 'Wanjiku',
                    'employee_id': 'EMP0004',
                    'role': 'accountant'
                },
                'profile_data': {
                    'department': finance_dept,
                    'job_title': 'Senior Accountant',
                    'hire_date': date.today() - timedelta(days=300),
                    'employment_type': 'full_time',
                    'work_location': 'office'
                },
                'salary_data': {
                    'basic_salary': 100000,
                    'allowances': 15000,
                    'pay_frequency': 'monthly'
                }
            },
            {
                'user_data': {
                    'email': '<EMAIL>',
                    'username': 'employee',
                    'first_name': 'James',
                    'last_name': 'Mwangi',
                    'employee_id': 'EMP0005',
                    'role': 'employee'
                },
                'profile_data': {
                    'department': it_dept,
                    'job_title': 'Software Developer',
                    'hire_date': date.today() - timedelta(days=180),
                    'employment_type': 'full_time',
                    'work_location': 'remote'
                },
                'salary_data': {
                    'basic_salary': 90000,
                    'allowances': 10000,
                    'pay_frequency': 'monthly'
                }
            },
            {
                'user_data': {
                    'email': '<EMAIL>',
                    'username': 'hr_staff',
                    'first_name': 'Mary',
                    'last_name': 'Njeri',
                    'employee_id': 'EMP0006',
                    'role': 'hr'
                },
                'profile_data': {
                    'department': hr_dept,
                    'job_title': 'HR Specialist',
                    'hire_date': date.today() - timedelta(days=250),
                    'employment_type': 'full_time',
                    'work_location': 'office'
                },
                'salary_data': {
                    'basic_salary': 85000,
                    'allowances': 12000,
                    'pay_frequency': 'monthly'
                }
            },
            {
                'user_data': {
                    'email': '<EMAIL>',
                    'username': 'sales_manager',
                    'first_name': 'David',
                    'last_name': 'Kiprotich',
                    'employee_id': 'EMP0007',
                    'role': 'supervisor'
                },
                'profile_data': {
                    'department': sales_dept,
                    'job_title': 'Sales Manager',
                    'hire_date': date.today() - timedelta(days=400),
                    'employment_type': 'full_time',
                    'work_location': 'office'
                },
                'salary_data': {
                    'basic_salary': 130000,
                    'allowances': 22000,
                    'pay_frequency': 'monthly'
                }
            },
            {
                'user_data': {
                    'email': '<EMAIL>',
                    'username': 'operations',
                    'first_name': 'Alice',
                    'last_name': 'Wambui',
                    'employee_id': 'EMP0008',
                    'role': 'employee'
                },
                'profile_data': {
                    'department': operations_dept,
                    'job_title': 'Operations Coordinator',
                    'hire_date': date.today() - timedelta(days=150),
                    'employment_type': 'full_time',
                    'work_location': 'office'
                },
                'salary_data': {
                    'basic_salary': 75000,
                    'allowances': 8000,
                    'pay_frequency': 'monthly'
                }
            },
            {
                'user_data': {
                    'email': '<EMAIL>',
                    'username': 'junior_dev',
                    'first_name': 'Peter',
                    'last_name': 'Ochieng',
                    'employee_id': 'EMP0009',
                    'role': 'employee'
                },
                'profile_data': {
                    'department': it_dept,
                    'job_title': 'Junior Developer',
                    'hire_date': date.today() - timedelta(days=90),
                    'employment_type': 'full_time',
                    'work_location': 'hybrid'
                },
                'salary_data': {
                    'basic_salary': 65000,
                    'allowances': 7000,
                    'pay_frequency': 'monthly'
                }
            }
        ]
        
        for emp_data in employees:
            # Create user
            user, created = User.objects.get_or_create(
                email=emp_data['user_data']['email'],
                defaults=emp_data['user_data']
            )
            
            if created:
                user.set_password('password123')
                user.save()
                
                # Create employee profile
                profile = EmployeeProfile.objects.create(
                    user=user,
                    **emp_data['profile_data']
                )
                
                # Create salary profile
                SalaryProfile.objects.create(
                    employee=user,
                    effective_from=emp_data['profile_data']['hire_date'],
                    **emp_data['salary_data']
                )
                
                # Create leave balances
                current_year = date.today().year
                leave_types = LeaveType.objects.all()
                
                for leave_type in leave_types:
                    if leave_type.max_days_per_year:
                        LeaveBalance.objects.create(
                            employee=user,
                            leave_type=leave_type,
                            year=current_year,
                            allocated_days=leave_type.max_days_per_year
                        )
                
                self.stdout.write(f'  ✅ Created employee: {user.first_name} {user.last_name}')
            else:
                self.stdout.write(f'  ⚠️ Employee already exists: {user.first_name} {user.last_name}')
