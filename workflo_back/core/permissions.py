"""
Custom permissions for WorkFlo Backend
Role-based access control and object-level permissions
"""

from rest_framework import permissions


class IsOwnerOrSupervisor(permissions.BasePermission):
    """
    Permission that allows access to owners or supervisors
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for any authenticated user
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions only for owner or supervisor
        if hasattr(obj, 'employee'):
            # Check if user is the employee or their supervisor
            if obj.employee == request.user:
                return True
            if hasattr(obj.employee, 'employee_profile') and obj.employee.employee_profile.supervisor == request.user:
                return True
        
        # Check if user is admin or HR
        return request.user.role in ['admin', 'hr']


class IsHROrAdmin(permissions.BasePermission):
    """
    Permission that allows access only to HR or Admin users
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in ['admin', 'hr']


class IsAccountantOrAdmin(permissions.BasePermission):
    """
    Permission that allows access only to Accountant or Admin users
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in ['admin', 'accountant']


class IsSupervisorOrAbove(permissions.BasePermission):
    """
    Permission that allows access to Supervisor, HR, or Admin users
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in ['admin', 'hr', 'supervisor']


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Permission that allows read access to all authenticated users,
    but write access only to the owner
    """
    
    def has_object_permission(self, request, view, obj):
        # Read permissions for any authenticated user
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Write permissions only for owner
        if hasattr(obj, 'employee'):
            return obj.employee == request.user
        elif hasattr(obj, 'user'):
            return obj.user == request.user
        
        return False


class CanManageEmployees(permissions.BasePermission):
    """
    Permission for users who can manage employees (HR, Admin, Supervisors)
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in ['admin', 'hr', 'supervisor']
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        # Admin and HR can manage all employees
        if user.role in ['admin', 'hr']:
            return True
        
        # Supervisors can only manage employees in their department
        if user.role == 'supervisor':
            if hasattr(obj, 'employee_profile') and obj.employee_profile.department:
                return obj.employee_profile.department.supervisor == user
            elif hasattr(obj, 'department'):
                return obj.department.supervisor == user
        
        return False


class CanViewFinancialData(permissions.BasePermission):
    """
    Permission for users who can view financial data (Admin, HR, Accountant, Employee for own data)
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        # Admin, HR, and Accountant can view all financial data
        if user.role in ['admin', 'hr', 'accountant']:
            return True
        
        # Employees can only view their own financial data
        if hasattr(obj, 'employee'):
            return obj.employee == user
        elif hasattr(obj, 'user'):
            return obj.user == user
        
        return False


class CanApproveLeave(permissions.BasePermission):
    """
    Permission for users who can approve leave applications
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in ['admin', 'hr', 'supervisor']
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        # Admin and HR can approve all leave applications
        if user.role in ['admin', 'hr']:
            return True
        
        # Supervisors can approve leave for their department employees
        if user.role == 'supervisor' and hasattr(obj, 'employee'):
            if hasattr(obj.employee, 'employee_profile') and obj.employee.employee_profile.department:
                return obj.employee.employee_profile.department.supervisor == user
        
        return False


class CanManagePayroll(permissions.BasePermission):
    """
    Permission for users who can manage payroll (Admin, HR, Accountant)
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in ['admin', 'hr', 'accountant']


class CanViewReports(permissions.BasePermission):
    """
    Permission for users who can view reports
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in ['admin', 'hr', 'supervisor', 'accountant']


class CanManageTraining(permissions.BasePermission):
    """
    Permission for users who can manage training programs
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in ['admin', 'hr']


class CanManageRecruitment(permissions.BasePermission):
    """
    Permission for users who can manage recruitment
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in ['admin', 'hr']


class CanManageDocuments(permissions.BasePermission):
    """
    Permission for users who can manage company documents
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in ['admin', 'hr']
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        # Admin and HR can manage all documents
        if user.role in ['admin', 'hr']:
            return True
        
        # Employees can only view documents (handled by view logic)
        return request.method in permissions.SAFE_METHODS


class CanAccessSystemSettings(permissions.BasePermission):
    """
    Permission for users who can access system settings
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role == 'admin'


class DepartmentBasedPermission(permissions.BasePermission):
    """
    Permission that filters access based on department hierarchy
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        user = request.user
        
        # Admin and HR have access to all departments
        if user.role in ['admin', 'hr']:
            return True
        
        # Supervisors have access to their department and sub-departments
        if user.role == 'supervisor':
            if hasattr(obj, 'department'):
                return self._can_access_department(user, obj.department)
            elif hasattr(obj, 'employee') and hasattr(obj.employee, 'employee_profile'):
                return self._can_access_department(user, obj.employee.employee_profile.department)
        
        # Employees can only access their own department data
        if hasattr(obj, 'employee'):
            return obj.employee == user
        
        return False
    
    def _can_access_department(self, user, department):
        """Check if user can access the given department"""
        if not department:
            return False
        
        # Check if user is supervisor of this department
        if department.supervisor == user:
            return True
        
        # Check if user is supervisor of parent department
        if department.parent_department and department.parent_department.supervisor == user:
            return True
        
        return False
