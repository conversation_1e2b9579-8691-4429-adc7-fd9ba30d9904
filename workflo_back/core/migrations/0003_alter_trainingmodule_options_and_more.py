# Generated by Django 5.2 on 2025-05-31 18:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_companydocument_options_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='trainingmodule',
            options={'ordering': ['-created_at'], 'verbose_name': 'Training Module', 'verbose_name_plural': 'Training Modules'},
        ),
        migrations.AlterModelOptions(
            name='trainingvenue',
            options={'ordering': ['name'], 'verbose_name': 'Training Venue', 'verbose_name_plural': 'Training Venues'},
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='category',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='content',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_training_modules', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='difficulty_level',
            field=models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced'), ('expert', 'Expert')], default='beginner', max_length=20),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='duration_hours',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='instructor_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='instructor_name',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='is_active',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='is_mandatory',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='learning_objectives',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='materials_url',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='prerequisites',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='trainingmodule',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='trainingvenue',
            name='address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='trainingvenue',
            name='capacity',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='trainingvenue',
            name='contact_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='trainingvenue',
            name='contact_person',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='trainingvenue',
            name='contact_phone',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='trainingvenue',
            name='equipment',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='trainingvenue',
            name='facilities',
            field=models.JSONField(blank=True, default=list),
        ),
        migrations.AddField(
            model_name='trainingvenue',
            name='hourly_rate',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='trainingvenue',
            name='status',
            field=models.CharField(choices=[('available', 'Available'), ('occupied', 'Occupied'), ('maintenance', 'Under Maintenance'), ('unavailable', 'Unavailable')], default='available', max_length=20),
        ),
        migrations.AddField(
            model_name='trainingvenue',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
