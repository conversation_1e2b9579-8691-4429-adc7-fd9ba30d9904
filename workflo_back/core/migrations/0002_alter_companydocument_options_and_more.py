# Generated by Django 5.2 on 2025-05-31 18:11

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='companydocument',
            options={'ordering': ['-created_at'], 'verbose_name': 'Company Document', 'verbose_name_plural': 'Company Documents'},
        ),
        migrations.AlterModelOptions(
            name='employeerecognition',
            options={'ordering': ['-created_at'], 'verbose_name': 'Employee Recognition', 'verbose_name_plural': 'Employee Recognitions'},
        ),
        migrations.AlterModelOptions(
            name='employeetrainingassignment',
            options={'verbose_name': 'Employee Training Assignment', 'verbose_name_plural': 'Employee Training Assignments'},
        ),
        migrations.AlterModelOptions(
            name='interviewschedule',
            options={'ordering': ['-scheduled_datetime'], 'verbose_name': 'Interview Schedule', 'verbose_name_plural': 'Interview Schedules'},
        ),
        migrations.AddField(
            model_name='companydocument',
            name='approved_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_company_documents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_company_documents', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='department_ids',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='effective_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='expiry_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='file_size',
            field=models.BigIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='file_type',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='is_public',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='requires_acknowledgment',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='role_access',
            field=models.TextField(default='["all"]'),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('archived', 'Archived'), ('expired', 'Expired')], default='active', max_length=20),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='companydocument',
            name='version',
            field=models.CharField(default='1.0', max_length=20),
        ),
        migrations.AddField(
            model_name='employeerecognition',
            name='achievement_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeerecognition',
            name='is_public',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='employeerecognition',
            name='points_awarded',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='employeerecognition',
            name='recognition_type',
            field=models.CharField(blank=True, choices=[('peer_to_peer', 'Peer to Peer'), ('manager_to_employee', 'Manager to Employee'), ('company_wide', 'Company Wide'), ('achievement', 'Achievement'), ('milestone', 'Milestone')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='assigned_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_trainings', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='certificate_url',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='completion_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='due_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='end_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='feedback',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='progress_percentage',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='score',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='start_date',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='status',
            field=models.CharField(choices=[('assigned', 'Assigned'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('overdue', 'Overdue')], default='assigned', max_length=20),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='employeetrainingassignment',
            name='venue',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assignments', to='core.trainingvenue'),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='agenda',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='created_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_interviews', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='duration_minutes',
            field=models.IntegerField(default=60),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='feedback',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='interview_type',
            field=models.CharField(default='in_person', max_length=50),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='interviewer_ids',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='location',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='meeting_link',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='next_steps',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='rating',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='recommendation',
            field=models.CharField(blank=True, choices=[('strong_hire', 'Strong Hire'), ('hire', 'Hire'), ('no_hire', 'No Hire'), ('strong_no_hire', 'Strong No Hire')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='status',
            field=models.CharField(choices=[('scheduled', 'Scheduled'), ('confirmed', 'Confirmed'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('rescheduled', 'Rescheduled'), ('no_show', 'No Show')], default='scheduled', max_length=20),
        ),
        migrations.AddField(
            model_name='interviewschedule',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='companydocument',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='company_documents', to='core.documentcategory'),
        ),
        migrations.AlterField(
            model_name='employeerecognition',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='recognitions', to='core.recognitioncategory'),
        ),
        migrations.AlterField(
            model_name='employeetrainingassignment',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='training_assignments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='employeetrainingassignment',
            name='training_module',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assignments', to='core.trainingmodule'),
        ),
        migrations.AlterField(
            model_name='interviewschedule',
            name='application',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='interviews', to='core.jobapplication'),
        ),
        migrations.AlterUniqueTogether(
            name='employeetrainingassignment',
            unique_together={('employee', 'training_module')},
        ),
        migrations.CreateModel(
            name='CandidateEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('evaluation_criteria', models.JSONField(default=dict)),
                ('overall_rating', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('technical_skills', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('communication_skills', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('cultural_fit', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('experience_relevance', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('strengths', models.TextField(blank=True, null=True)),
                ('concerns', models.TextField(blank=True, null=True)),
                ('recommendation', models.CharField(blank=True, choices=[('strong_hire', 'Strong Hire'), ('hire', 'Hire'), ('no_hire', 'No Hire'), ('strong_no_hire', 'Strong No Hire')], max_length=20, null=True)),
                ('comments', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('application', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluations', to='core.jobapplication')),
                ('evaluator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='candidate_evaluations', to=settings.AUTH_USER_MODEL)),
                ('interview', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='evaluations', to='core.interviewschedule')),
            ],
            options={
                'verbose_name': 'Candidate Evaluation',
                'verbose_name_plural': 'Candidate Evaluations',
                'db_table': 'candidate_evaluations',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeBenefit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('benefit_type', models.CharField(max_length=50)),
                ('benefit_name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('value_type', models.CharField(blank=True, choices=[('fixed_amount', 'Fixed Amount'), ('percentage', 'Percentage'), ('days', 'Days'), ('coverage', 'Coverage')], max_length=20, null=True)),
                ('value', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('currency', models.CharField(default='KSH', max_length=3)),
                ('provider', models.CharField(blank=True, max_length=100, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('pending', 'Pending'), ('expired', 'Expired')], default='active', max_length=20)),
                ('employee_contribution', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('employer_contribution', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('coverage_details', models.TextField(blank=True, null=True)),
                ('dependents_covered', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='benefits', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Employee Benefit',
                'verbose_name_plural': 'Employee Benefits',
                'db_table': 'employee_benefits',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feedback_type', models.CharField(choices=[('suggestion', 'Suggestion'), ('complaint', 'Complaint'), ('compliment', 'Compliment'), ('general', 'General'), ('anonymous', 'Anonymous')], max_length=20)),
                ('subject', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('category', models.CharField(blank=True, max_length=100, null=True)),
                ('is_anonymous', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('submitted', 'Submitted'), ('under_review', 'Under Review'), ('in_progress', 'In Progress'), ('resolved', 'Resolved'), ('closed', 'Closed')], default='submitted', max_length=20)),
                ('priority', models.CharField(default='medium', max_length=10)),
                ('response', models.TextField(blank=True, null=True)),
                ('response_date', models.DateTimeField(blank=True, null=True)),
                ('resolved_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_feedback', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='feedback_given', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Employee Feedback',
                'verbose_name_plural': 'Employee Feedback',
                'db_table': 'employee_feedback',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OvertimeApprovalWorkflow',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('requires_supervisor_approval', models.BooleanField(default=True)),
                ('requires_admin_approval', models.BooleanField(default=False)),
                ('requires_hr_approval', models.BooleanField(default=False)),
                ('supervisor_approval_threshold', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True)),
                ('admin_approval_threshold', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True)),
                ('auto_approval_threshold', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True)),
                ('approval_deadline_hours', models.IntegerField(default=24)),
                ('advance_notice_hours', models.IntegerField(default=4)),
                ('escalation_enabled', models.BooleanField(default=True)),
                ('escalation_hours', models.IntegerField(default=24)),
                ('escalate_to_admin', models.BooleanField(default=True)),
                ('escalate_to_hr', models.BooleanField(default=False)),
                ('notify_employee', models.BooleanField(default=True)),
                ('notify_supervisor', models.BooleanField(default=True)),
                ('notify_admin', models.BooleanField(default=False)),
                ('notify_hr', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_overtime_workflows', to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='overtime_workflows', to='core.department')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_overtime_workflows', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Overtime Approval Workflow',
                'verbose_name_plural': 'Overtime Approval Workflows',
                'db_table': 'overtime_approval_workflows',
            },
        ),
        migrations.CreateModel(
            name='OvertimeCalculation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('base_hourly_rate', models.DecimalField(decimal_places=2, max_digits=10)),
                ('overtime_rate_multiplier', models.DecimalField(decimal_places=2, max_digits=3)),
                ('calculated_overtime_rate', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_overtime_hours', models.DecimalField(decimal_places=2, max_digits=4)),
                ('regular_hours_equivalent', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True)),
                ('gross_overtime_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('overtime_tax_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('overtime_tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('overtime_nssf_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('overtime_nhif_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('overtime_housing_levy', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_overtime_deductions', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('net_overtime_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('calculation_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('calculation_method', models.CharField(choices=[('standard', 'Standard'), ('holiday', 'Holiday'), ('emergency', 'Emergency')], default='standard', max_length=50)),
                ('currency', models.CharField(default='KSH', max_length=3)),
                ('exchange_rate', models.DecimalField(decimal_places=4, default=1.0, max_digits=10)),
                ('status', models.CharField(choices=[('calculated', 'Calculated'), ('approved', 'Approved'), ('included_in_payroll', 'Included in Payroll'), ('paid', 'Paid')], default='calculated', max_length=20)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('included_in_payroll', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_overtime_calculations', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_overtime_calculations', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='overtime_calculations', to=settings.AUTH_USER_MODEL)),
                ('overtime_record', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='calculation', to='core.overtimerecord')),
                ('pay_cycle', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='overtime_calculations', to='core.paycycle')),
                ('payroll_record', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='overtime_calculations', to='core.payrollrecord')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_overtime_calculations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Overtime Calculation',
                'verbose_name_plural': 'Overtime Calculations',
                'db_table': 'overtime_calculations',
            },
        ),
        migrations.CreateModel(
            name='TrainingSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('start_datetime', models.DateTimeField()),
                ('end_datetime', models.DateTimeField()),
                ('max_participants', models.IntegerField(blank=True, null=True)),
                ('current_participants', models.IntegerField(default=0)),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='scheduled', max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_training_sessions', to=settings.AUTH_USER_MODEL)),
                ('instructor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='instructed_sessions', to=settings.AUTH_USER_MODEL)),
                ('training_module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='core.trainingmodule')),
                ('venue', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sessions', to='core.trainingvenue')),
            ],
            options={
                'verbose_name': 'Training Session',
                'verbose_name_plural': 'Training Sessions',
                'db_table': 'training_sessions',
                'ordering': ['-start_datetime'],
            },
        ),
        migrations.CreateModel(
            name='WellnessProgram',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('program_type', models.CharField(choices=[('fitness', 'Fitness'), ('mental_health', 'Mental Health'), ('nutrition', 'Nutrition'), ('stress_management', 'Stress Management'), ('work_life_balance', 'Work Life Balance'), ('health_screening', 'Health Screening')], max_length=50)),
                ('provider', models.CharField(blank=True, max_length=100, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('enrollment_deadline', models.DateField(blank=True, null=True)),
                ('max_participants', models.IntegerField(blank=True, null=True)),
                ('current_participants', models.IntegerField(default=0)),
                ('cost_per_participant', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('company_contribution', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('employee_contribution', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('paused', 'Paused'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('requirements', models.TextField(blank=True, null=True)),
                ('benefits', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_wellness_programs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Wellness Program',
                'verbose_name_plural': 'Wellness Programs',
                'db_table': 'wellness_programs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WorkflowDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('workflow_type', models.CharField(choices=[('leave_approval', 'Leave Approval'), ('overtime_approval', 'Overtime Approval'), ('expense_approval', 'Expense Approval'), ('document_approval', 'Document Approval'), ('recruitment', 'Recruitment'), ('performance_review', 'Performance Review'), ('salary_adjustment', 'Salary Adjustment'), ('custom', 'Custom')], max_length=50)),
                ('version', models.CharField(default='1.0', max_length=20)),
                ('workflow_steps', models.JSONField(default=list)),
                ('approval_rules', models.JSONField(default=dict)),
                ('escalation_rules', models.JSONField(default=dict)),
                ('notification_settings', models.JSONField(default=dict)),
                ('department_ids', models.TextField(blank=True, null=True)),
                ('role_access', models.TextField(default='["all"]')),
                ('sla_hours', models.IntegerField(blank=True, null=True)),
                ('escalation_hours', models.IntegerField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('inactive', 'Inactive'), ('archived', 'Archived')], default='draft', max_length=20)),
                ('is_default', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_workflows', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_workflows', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Workflow Definition',
                'verbose_name_plural': 'Workflow Definitions',
                'db_table': 'workflow_definitions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='WorkflowInstance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('related_object_type', models.CharField(blank=True, max_length=50, null=True)),
                ('related_object_id', models.IntegerField(blank=True, null=True)),
                ('current_step', models.IntegerField(default=0)),
                ('status', models.CharField(choices=[('initiated', 'Initiated'), ('in_progress', 'In Progress'), ('pending_approval', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled'), ('completed', 'Completed'), ('escalated', 'Escalated'), ('expired', 'Expired')], default='initiated', max_length=20)),
                ('workflow_data', models.JSONField(default=dict)),
                ('approval_history', models.JSONField(default=list)),
                ('initiated_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('due_date', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('escalated_at', models.DateTimeField(blank=True, null=True)),
                ('final_decision', models.CharField(blank=True, max_length=20, null=True)),
                ('final_comments', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('current_approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='pending_approvals', to=settings.AUTH_USER_MODEL)),
                ('final_approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='final_approved_workflows', to=settings.AUTH_USER_MODEL)),
                ('initiated_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='initiated_workflows', to=settings.AUTH_USER_MODEL)),
                ('workflow_definition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='instances', to='core.workflowdefinition')),
            ],
            options={
                'verbose_name': 'Workflow Instance',
                'verbose_name_plural': 'Workflow Instances',
                'db_table': 'workflow_instances',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DocumentAcknowledgment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('acknowledged_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('digital_signature', models.TextField(blank=True, null=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='acknowledgments', to='core.companydocument')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_acknowledgments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Document Acknowledgment',
                'verbose_name_plural': 'Document Acknowledgments',
                'db_table': 'document_acknowledgments',
                'ordering': ['-acknowledged_at'],
                'unique_together': {('document', 'employee')},
            },
        ),
        migrations.CreateModel(
            name='OvertimeBudget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('budget_year', models.IntegerField()),
                ('budget_month', models.IntegerField(blank=True, null=True)),
                ('allocated_hours', models.DecimalField(decimal_places=2, max_digits=6)),
                ('allocated_amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('used_hours', models.DecimalField(decimal_places=2, default=0, max_digits=6)),
                ('used_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('warning_threshold_percentage', models.IntegerField(default=80)),
                ('block_threshold_percentage', models.IntegerField(default=100)),
                ('is_active', models.BooleanField(default=True)),
                ('budget_exceeded', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_overtime_budgets', to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='overtime_budgets', to='core.department')),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='overtime_budgets', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_overtime_budgets', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Overtime Budget',
                'verbose_name_plural': 'Overtime Budgets',
                'db_table': 'overtime_budgets',
                'unique_together': {('department', 'employee', 'budget_year', 'budget_month')},
            },
        ),
        migrations.CreateModel(
            name='TrainingSessionParticipant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attendance_status', models.CharField(choices=[('registered', 'Registered'), ('attended', 'Attended'), ('absent', 'Absent'), ('cancelled', 'Cancelled')], default='registered', max_length=20)),
                ('completion_status', models.CharField(choices=[('not_started', 'Not Started'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed')], default='not_started', max_length=20)),
                ('score', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('feedback', models.TextField(blank=True, null=True)),
                ('certificate_issued', models.BooleanField(default=False)),
                ('registered_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='training_participations', to=settings.AUTH_USER_MODEL)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='participants', to='core.trainingsession')),
            ],
            options={
                'verbose_name': 'Training Session Participant',
                'verbose_name_plural': 'Training Session Participants',
                'db_table': 'training_session_participants',
                'unique_together': {('session', 'employee')},
            },
        ),
        migrations.CreateModel(
            name='WellnessProgramEnrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('enrollment_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('start_date', models.DateField(blank=True, null=True)),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('enrolled', 'Enrolled'), ('active', 'Active'), ('completed', 'Completed'), ('dropped', 'Dropped'), ('cancelled', 'Cancelled')], default='enrolled', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0)),
                ('feedback', models.TextField(blank=True, null=True)),
                ('satisfaction_rating', models.IntegerField(blank=True, null=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='wellness_enrollments', to=settings.AUTH_USER_MODEL)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollments', to='core.wellnessprogram')),
            ],
            options={
                'verbose_name': 'Wellness Program Enrollment',
                'verbose_name_plural': 'Wellness Program Enrollments',
                'db_table': 'wellness_program_enrollments',
                'ordering': ['-enrollment_date'],
                'unique_together': {('program', 'employee')},
            },
        ),
    ]
