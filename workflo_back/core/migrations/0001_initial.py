# Generated by Django 5.2 on 2025-05-31 17:54

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='BiostarDevice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('biostar_device_id', models.CharField(max_length=100, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('port', models.IntegerField(blank=True, null=True)),
                ('location', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('device_type', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.CharField(choices=[('online', 'Online'), ('offline', 'Offline'), ('maintenance', 'Maintenance')], default='offline', max_length=20)),
                ('last_seen', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'BioStar Device',
                'verbose_name_plural': 'BioStar Devices',
                'db_table': 'biostar_devices',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CompanyInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(max_length=255)),
                ('address', models.TextField(blank=True, null=True)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('timezone', models.CharField(default='Africa/Nairobi', max_length=50)),
                ('currency', models.CharField(default='KSH', max_length=3)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'company_info',
            },
        ),
        migrations.CreateModel(
            name='DocumentCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'document_categories',
            },
        ),
        migrations.CreateModel(
            name='EmployeeSurvey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('survey_type', models.CharField(max_length=50)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'employee_surveys',
            },
        ),
        migrations.CreateModel(
            name='JobApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(max_length=100)),
                ('last_name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('applied_date', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'job_applications',
            },
        ),
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('max_days_per_year', models.IntegerField(blank=True, null=True)),
                ('carry_forward_allowed', models.BooleanField(default=False)),
                ('max_carry_forward_days', models.IntegerField(blank=True, null=True)),
                ('requires_approval', models.BooleanField(default=True)),
                ('is_paid', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name': 'Leave Type',
                'verbose_name_plural': 'Leave Types',
                'db_table': 'leave_types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('event_type', models.CharField(max_length=100)),
                ('subject_template', models.TextField()),
                ('body_template', models.TextField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'notification_templates',
            },
        ),
        migrations.CreateModel(
            name='RecognitionCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('points_value', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'recognition_categories',
            },
        ),
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('setting_key', models.CharField(max_length=100, unique=True)),
                ('setting_value', models.TextField(blank=True, null=True)),
                ('setting_type', models.CharField(default='string', max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'system_settings',
            },
        ),
        migrations.CreateModel(
            name='TrainingModule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'training_modules',
            },
        ),
        migrations.CreateModel(
            name='TrainingVenue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'training_venues',
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('employee_id', models.CharField(max_length=50, unique=True)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('profile_picture', models.TextField(blank=True, null=True)),
                ('role', models.CharField(choices=[('admin', 'Admin'), ('hr', 'HR'), ('supervisor', 'Supervisor'), ('accountant', 'Accountant'), ('employee', 'Employee')], default='employee', max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('last_login', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_users', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='deleted_users', to=settings.AUTH_USER_MODEL)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_users', to=settings.AUTH_USER_MODEL)),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'db_table': 'users',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(max_length=50)),
                ('activity_description', models.TextField()),
                ('module', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'activity_logs',
            },
        ),
        migrations.CreateModel(
            name='AttendanceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('check_in', models.DateTimeField(blank=True, null=True)),
                ('check_out', models.DateTimeField(blank=True, null=True)),
                ('break_start', models.DateTimeField(blank=True, null=True)),
                ('break_end', models.DateTimeField(blank=True, null=True)),
                ('total_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('regular_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('overtime_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('break_time', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('status', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent'), ('late', 'Late'), ('early_out', 'Early Out'), ('half_day', 'Half Day')], default='present', max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('biostar_synced', models.BooleanField(default=False)),
                ('biostar_event_ids', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Attendance Record',
                'verbose_name_plural': 'Attendance Records',
                'db_table': 'attendance_records',
                'ordering': ['-date'],
                'unique_together': {('employee', 'date')},
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(max_length=50)),
                ('table_name', models.CharField(max_length=100)),
                ('record_id', models.IntegerField(blank=True, null=True)),
                ('old_values', models.JSONField(blank=True, null=True)),
                ('new_values', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'audit_logs',
            },
        ),
        migrations.CreateModel(
            name='BankProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bank_name', models.CharField(max_length=100)),
                ('bank_code', models.CharField(blank=True, max_length=20, null=True)),
                ('branch_name', models.CharField(blank=True, max_length=100, null=True)),
                ('branch_code', models.CharField(blank=True, max_length=20, null=True)),
                ('account_number', models.CharField(max_length=50)),
                ('account_name', models.CharField(max_length=255)),
                ('account_type', models.CharField(blank=True, choices=[('savings', 'Savings'), ('current', 'Current'), ('fixed_deposit', 'Fixed Deposit')], max_length=20, null=True)),
                ('swift_code', models.CharField(blank=True, max_length=20, null=True)),
                ('is_primary', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_bank_profiles', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bank_profiles', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_bank_profiles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Bank Profile',
                'verbose_name_plural': 'Bank Profiles',
                'db_table': 'bank_profiles',
            },
        ),
        migrations.CreateModel(
            name='BiostarEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('biostar_event_id', models.CharField(max_length=100, unique=True)),
                ('device_id', models.CharField(blank=True, max_length=100, null=True)),
                ('device_name', models.CharField(blank=True, max_length=255, null=True)),
                ('event_type', models.CharField(choices=[('ENTRY', 'Entry'), ('EXIT', 'Exit'), ('DENIED', 'Denied')], max_length=20)),
                ('event_datetime', models.DateTimeField()),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('processed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('attendance_record', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='biostar_events', to='core.attendancerecord')),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='biostar_events', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'BioStar Event',
                'verbose_name_plural': 'BioStar Events',
                'db_table': 'biostar_events',
                'ordering': ['-event_datetime'],
            },
        ),
        migrations.CreateModel(
            name='CompanyHoliday',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('date', models.DateField()),
                ('description', models.TextField(blank=True, null=True)),
                ('is_recurring', models.BooleanField(default=False)),
                ('applies_to_all', models.BooleanField(default=True)),
                ('department_ids', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_holidays', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Company Holiday',
                'verbose_name_plural': 'Company Holidays',
                'db_table': 'company_holidays',
                'ordering': ['date'],
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('budget', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_departments', to=settings.AUTH_USER_MODEL)),
                ('parent_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sub_departments', to='core.department')),
                ('supervisor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_departments', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_departments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Department',
                'verbose_name_plural': 'Departments',
                'db_table': 'departments',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CompanyDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('file_path', models.TextField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.documentcategory')),
            ],
            options={
                'db_table': 'company_documents',
            },
        ),
        migrations.CreateModel(
            name='EmergencyContact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contact_name', models.CharField(max_length=100)),
                ('relationship', models.CharField(max_length=50)),
                ('phone_number', models.CharField(max_length=20)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('priority_order', models.IntegerField(default=1, validators=[django.core.validators.MinValueValidator(1)])),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_emergency_contacts', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='emergency_contacts', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_emergency_contacts', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Emergency Contact',
                'verbose_name_plural': 'Emergency Contacts',
                'db_table': 'emergency_contacts',
                'ordering': ['priority_order'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_name', models.CharField(max_length=255)),
                ('file_path', models.TextField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.documentcategory')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'employee_documents',
            },
        ),
        migrations.CreateModel(
            name='EmployeeProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('job_title', models.CharField(max_length=100)),
                ('hire_date', models.DateField()),
                ('employment_type', models.CharField(choices=[('full_time', 'Full Time'), ('part_time', 'Part Time'), ('contract', 'Contract'), ('intern', 'Intern')], max_length=20)),
                ('work_location', models.CharField(choices=[('office', 'Office'), ('remote', 'Remote'), ('hybrid', 'Hybrid')], max_length=20)),
                ('nssf_number', models.CharField(blank=True, max_length=20, null=True)),
                ('nhif_number', models.CharField(blank=True, max_length=20, null=True)),
                ('kra_pin', models.CharField(blank=True, max_length=20, null=True)),
                ('national_id', models.CharField(blank=True, max_length=20, null=True)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other'), ('prefer_not_to_say', 'Prefer not to say')], max_length=30, null=True)),
                ('marital_status', models.CharField(blank=True, choices=[('single', 'Single'), ('married', 'Married'), ('divorced', 'Divorced'), ('widowed', 'Widowed')], max_length=20, null=True)),
                ('nationality', models.CharField(default='Kenyan', max_length=50)),
                ('address', models.TextField(blank=True, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('state', models.CharField(blank=True, max_length=100, null=True)),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True)),
                ('country', models.CharField(default='Kenya', max_length=100)),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('terminated', 'Terminated'), ('on_leave', 'On Leave')], default='active', max_length=20)),
                ('termination_date', models.DateField(blank=True, null=True)),
                ('termination_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employee_profiles', to=settings.AUTH_USER_MODEL)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employees', to='core.department')),
                ('supervisor', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_employees', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_employee_profiles', to=settings.AUTH_USER_MODEL)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='employee_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Employee Profile',
                'verbose_name_plural': 'Employee Profiles',
                'db_table': 'employee_profiles',
            },
        ),
        migrations.CreateModel(
            name='InterviewSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_datetime', models.DateTimeField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('application', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.jobapplication')),
            ],
            options={
                'db_table': 'interview_schedules',
            },
        ),
        migrations.CreateModel(
            name='JobPosting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.department')),
            ],
            options={
                'db_table': 'job_postings',
            },
        ),
        migrations.AddField(
            model_name='jobapplication',
            name='job_posting',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.jobposting'),
        ),
        migrations.CreateModel(
            name='LeaveApplication',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('days_requested', models.DecimalField(decimal_places=2, max_digits=5)),
                ('reason', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('reviewer_comments', models.TextField(blank=True, null=True)),
                ('attachment_url', models.TextField(blank=True, null=True)),
                ('applied_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leave_applications', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_applications', to=settings.AUTH_USER_MODEL)),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='core.leavetype')),
            ],
            options={
                'verbose_name': 'Leave Application',
                'verbose_name_plural': 'Leave Applications',
                'db_table': 'leave_applications',
                'ordering': ['-applied_date'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'notifications',
            },
        ),
        migrations.CreateModel(
            name='EmailLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recipient_email', models.EmailField(max_length=254)),
                ('subject', models.CharField(max_length=255)),
                ('status', models.CharField(default='pending', max_length=20)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('notification', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.notification')),
            ],
            options={
                'db_table': 'email_logs',
            },
        ),
        migrations.CreateModel(
            name='OvertimeType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('rate_multiplier', models.DecimalField(decimal_places=2, default=1.5, max_digits=3)),
                ('max_hours_per_day', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True)),
                ('max_hours_per_week', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True)),
                ('max_hours_per_month', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('requires_pre_approval', models.BooleanField(default=True)),
                ('auto_approve_threshold', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_overtime_types', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_overtime_types', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Overtime Type',
                'verbose_name_plural': 'Overtime Types',
                'db_table': 'overtime_types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='OvertimeRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_date', models.DateField()),
                ('planned_start_time', models.TimeField()),
                ('planned_end_time', models.TimeField()),
                ('planned_hours', models.DecimalField(decimal_places=2, max_digits=4)),
                ('reason', models.TextField()),
                ('justification', models.TextField(blank=True, null=True)),
                ('project_code', models.CharField(blank=True, max_length=50, null=True)),
                ('department_approval_required', models.BooleanField(default=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled'), ('completed', 'Completed')], default='pending', max_length=20)),
                ('supervisor_approved_at', models.DateTimeField(blank=True, null=True)),
                ('supervisor_comments', models.TextField(blank=True, null=True)),
                ('admin_approved_at', models.DateTimeField(blank=True, null=True)),
                ('admin_comments', models.TextField(blank=True, null=True)),
                ('final_approved_at', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True, null=True)),
                ('actual_start_time', models.DateTimeField(blank=True, null=True)),
                ('actual_end_time', models.DateTimeField(blank=True, null=True)),
                ('actual_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True)),
                ('completion_notes', models.TextField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('admin_approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='admin_approved_overtime', to=settings.AUTH_USER_MODEL)),
                ('completed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_overtime', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_overtime_requests', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='overtime_requests', to=settings.AUTH_USER_MODEL)),
                ('final_approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='final_approved_overtime', to=settings.AUTH_USER_MODEL)),
                ('requested_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='requested_overtime', to=settings.AUTH_USER_MODEL)),
                ('supervisor_approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervisor_approved_overtime', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_overtime_requests', to=settings.AUTH_USER_MODEL)),
                ('overtime_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requests', to='core.overtimetype')),
            ],
            options={
                'verbose_name': 'Overtime Request',
                'verbose_name_plural': 'Overtime Requests',
                'db_table': 'overtime_requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PasswordResetToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=255, unique=True)),
                ('expires_at', models.DateTimeField()),
                ('used', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='password_reset_tokens', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Password Reset Token',
                'verbose_name_plural': 'Password Reset Tokens',
                'db_table': 'password_reset_tokens',
            },
        ),
        migrations.CreateModel(
            name='PayCycle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pay_period', models.CharField(max_length=50)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('pay_date', models.DateField()),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('processing', 'Processing'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('total_employees', models.IntegerField(default=0)),
                ('total_gross_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_net_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_deductions', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_pay_cycles', to=settings.AUTH_USER_MODEL)),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_pay_cycles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Pay Cycle',
                'verbose_name_plural': 'Pay Cycles',
                'db_table': 'pay_cycles',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='PayrollAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjustment_type', models.CharField(choices=[('bonus', 'Bonus'), ('deduction', 'Deduction'), ('refund', 'Refund'), ('allowance', 'Allowance')], max_length=20)),
                ('category', models.CharField(blank=True, max_length=50, null=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15)),
                ('adjustment_effect', models.CharField(choices=[('increase', 'Increase'), ('decrease', 'Decrease')], max_length=10)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_recurring', models.BooleanField(default=False)),
                ('effective_date', models.DateField(blank=True, null=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_payroll_adjustments', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payroll_adjustments', to=settings.AUTH_USER_MODEL)),
                ('pay_cycle', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='adjustments', to='core.paycycle')),
            ],
            options={
                'verbose_name': 'Payroll Adjustment',
                'verbose_name_plural': 'Payroll Adjustments',
                'db_table': 'payroll_adjustments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PerformanceReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('review_period_start', models.DateField()),
                ('review_period_end', models.DateField()),
                ('review_type', models.CharField(blank=True, choices=[('annual', 'Annual'), ('quarterly', 'Quarterly'), ('probation', 'Probation'), ('project', 'Project')], max_length=20, null=True)),
                ('overall_rating', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('technical_skills', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('communication', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('teamwork', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('leadership', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('problem_solving', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('time_management', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True)),
                ('strengths', models.TextField(blank=True, null=True)),
                ('areas_for_improvement', models.TextField(blank=True, null=True)),
                ('reviewer_comments', models.TextField(blank=True, null=True)),
                ('employee_comments', models.TextField(blank=True, null=True)),
                ('goals_for_next_period', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending_employee', 'Pending Employee'), ('pending_manager', 'Pending Manager'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('employee_acknowledged', models.BooleanField(default=False)),
                ('employee_acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('due_date', models.DateField(blank=True, null=True)),
                ('completed_date', models.DateField(blank=True, null=True)),
                ('next_review_date', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_reviews', to=settings.AUTH_USER_MODEL)),
                ('reviewer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conducted_reviews', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Performance Review',
                'verbose_name_plural': 'Performance Reviews',
                'db_table': 'performance_reviews',
                'ordering': ['-review_period_end'],
            },
        ),
        migrations.CreateModel(
            name='PerformanceGoal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True, null=True)),
                ('target_date', models.DateField(blank=True, null=True)),
                ('priority', models.CharField(blank=True, choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], max_length=10, null=True)),
                ('status', models.CharField(choices=[('not_started', 'Not Started'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('overdue', 'Overdue')], default='not_started', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('completion_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_goals', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performance_goals', to=settings.AUTH_USER_MODEL)),
                ('review', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='goals', to='core.performancereview')),
            ],
            options={
                'verbose_name': 'Performance Goal',
                'verbose_name_plural': 'Performance Goals',
                'db_table': 'performance_goals',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PerformanceReviewTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('review_type', models.CharField(blank=True, choices=[('annual', 'Annual'), ('quarterly', 'Quarterly'), ('probation', 'Probation'), ('project', 'Project')], max_length=20, null=True)),
                ('criteria', models.JSONField(default=dict)),
                ('rating_scale', models.IntegerField(default=5)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_review_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Performance Review Template',
                'verbose_name_plural': 'Performance Review Templates',
                'db_table': 'performance_review_templates',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='performancereview',
            name='template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviews', to='core.performancereviewtemplate'),
        ),
        migrations.CreateModel(
            name='EmployeeRecognition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('nominator', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='recognitions_given', to=settings.AUTH_USER_MODEL)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recognitions_received', to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.recognitioncategory')),
            ],
            options={
                'db_table': 'employee_recognitions',
            },
        ),
        migrations.CreateModel(
            name='SalaryProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=15)),
                ('hourly_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('currency', models.CharField(default='KSH', max_length=3)),
                ('pay_frequency', models.CharField(choices=[('monthly', 'Monthly'), ('bi_weekly', 'Bi-weekly'), ('weekly', 'Weekly')], default='monthly', max_length=20)),
                ('allowances', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('overtime_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('commission_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('tax_exemption_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('tax_relief_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('effective_from', models.DateField()),
                ('effective_to', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_salary_profiles', to=settings.AUTH_USER_MODEL)),
                ('employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='salary_profile', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_salary_profiles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Salary Profile',
                'verbose_name_plural': 'Salary Profiles',
                'db_table': 'salary_profiles',
            },
        ),
        migrations.CreateModel(
            name='SalaryAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjustment_type', models.CharField(choices=[('salary_increase', 'Salary Increase'), ('salary_decrease', 'Salary Decrease'), ('promotion', 'Promotion'), ('demotion', 'Demotion'), ('role_change', 'Role Change'), ('annual_review', 'Annual Review'), ('market_adjustment', 'Market Adjustment'), ('performance_adjustment', 'Performance Adjustment')], max_length=30)),
                ('previous_basic_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('previous_allowances', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('previous_hourly_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('previous_overtime_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('new_basic_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('new_allowances', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('new_hourly_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('new_overtime_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('percentage_change', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('amount_change', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('reason', models.TextField()),
                ('justification', models.TextField(blank=True, null=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('effective_from', models.DateField()),
                ('effective_to', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_salary_adjustments', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_salary_adjustments', to=settings.AUTH_USER_MODEL)),
                ('requested_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='requested_salary_adjustments', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_salary_adjustments', to=settings.AUTH_USER_MODEL)),
                ('salary_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='adjustments', to='core.salaryprofile')),
            ],
            options={
                'verbose_name': 'Salary Adjustment',
                'verbose_name_plural': 'Salary Adjustments',
                'db_table': 'salary_adjustments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SurveyResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('responses', models.JSONField(default=dict)),
                ('submitted_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('survey', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.employeesurvey')),
            ],
            options={
                'db_table': 'survey_responses',
            },
        ),
        migrations.CreateModel(
            name='EmployeeTrainingAssignment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('training_module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.trainingmodule')),
            ],
            options={
                'db_table': 'employee_training_assignments',
            },
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('access_token', models.TextField()),
                ('refresh_token', models.TextField()),
                ('expires_at', models.DateTimeField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('last_used', models.DateTimeField(default=django.utils.timezone.now)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Session',
                'verbose_name_plural': 'User Sessions',
                'db_table': 'user_sessions',
            },
        ),
        migrations.CreateModel(
            name='LeaveBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('allocated_days', models.DecimalField(decimal_places=2, max_digits=5)),
                ('used_days', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('pending_days', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('carried_forward_days', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_balances', to=settings.AUTH_USER_MODEL)),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='balances', to='core.leavetype')),
            ],
            options={
                'verbose_name': 'Leave Balance',
                'verbose_name_plural': 'Leave Balances',
                'db_table': 'leave_balances',
                'ordering': ['-year', 'leave_type__name'],
                'unique_together': {('employee', 'leave_type', 'year')},
            },
        ),
        migrations.CreateModel(
            name='OvertimeRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overtime_date', models.DateField()),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField()),
                ('total_hours', models.DecimalField(decimal_places=2, max_digits=4)),
                ('rate_multiplier', models.DecimalField(decimal_places=2, max_digits=3)),
                ('regular_hourly_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('overtime_hourly_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('total_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('currency', models.CharField(default='KSH', max_length=3)),
                ('overtime_category', models.CharField(blank=True, choices=[('weekday', 'Weekday'), ('weekend', 'Weekend'), ('holiday', 'Holiday'), ('emergency', 'Emergency'), ('project_deadline', 'Project Deadline')], max_length=30, null=True)),
                ('is_emergency', models.BooleanField(default=False)),
                ('is_pre_approved', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('paid', 'Paid')], default='pending', max_length=20)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('approval_comments', models.TextField(blank=True, null=True)),
                ('included_in_payroll', models.BooleanField(default=False)),
                ('payroll_processed_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_overtime_records', to=settings.AUTH_USER_MODEL)),
                ('attendance_record', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='overtime_records', to='core.attendancerecord')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_overtime_records', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='overtime_records', to=settings.AUTH_USER_MODEL)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_overtime_records', to=settings.AUTH_USER_MODEL)),
                ('overtime_request', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='records', to='core.overtimerequest')),
                ('overtime_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='records', to='core.overtimetype')),
                ('pay_cycle', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='overtime_records', to='core.paycycle')),
            ],
            options={
                'verbose_name': 'Overtime Record',
                'verbose_name_plural': 'Overtime Records',
                'db_table': 'overtime_records',
                'ordering': ['-overtime_date', '-start_time'],
                'unique_together': {('employee', 'overtime_date', 'start_time')},
            },
        ),
        migrations.CreateModel(
            name='PayrollRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=15)),
                ('allowances', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('overtime_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('bonuses', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('gross_salary', models.DecimalField(decimal_places=2, max_digits=15)),
                ('tax_deduction', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('nssf_deduction', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('nhif_deduction', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('housing_levy', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('loan_deductions', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('other_deductions', models.DecimalField(decimal_places=2, default=0, max_digits=15)),
                ('total_deductions', models.DecimalField(decimal_places=2, max_digits=15)),
                ('net_salary', models.DecimalField(decimal_places=2, max_digits=15)),
                ('currency', models.CharField(default='KSH', max_length=3)),
                ('exchange_rate', models.DecimalField(decimal_places=4, default=1.0, max_digits=10)),
                ('payment_method', models.CharField(default='bank_transfer', max_length=20)),
                ('payment_reference', models.CharField(blank=True, max_length=100, null=True)),
                ('payment_status', models.CharField(choices=[('draft', 'Draft'), ('pending_approval', 'Pending Approval'), ('approved', 'Approved'), ('processing', 'Processing'), ('paid', 'Paid'), ('failed', 'Failed'), ('cancelled', 'Cancelled'), ('on_hold', 'On Hold')], default='draft', max_length=20)),
                ('payment_date', models.DateTimeField(blank=True, null=True)),
                ('working_days', models.IntegerField(blank=True, null=True)),
                ('days_worked', models.IntegerField(blank=True, null=True)),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('leave_days_deducted', models.DecimalField(decimal_places=2, default=0, max_digits=5)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payroll_records', to=settings.AUTH_USER_MODEL)),
                ('pay_cycle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payroll_records', to='core.paycycle')),
            ],
            options={
                'verbose_name': 'Payroll Record',
                'verbose_name_plural': 'Payroll Records',
                'db_table': 'payroll_records',
                'ordering': ['-pay_cycle__start_date'],
                'unique_together': {('employee', 'pay_cycle')},
            },
        ),
    ]
