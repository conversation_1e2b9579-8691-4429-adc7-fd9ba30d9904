from django.db import models
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from .auth import User


class PerformanceReviewTemplate(models.Model):
    """
    Performance review templates
    Based on performance_review_templates table from database.txt
    """
    REVIEW_TYPE_CHOICES = [
        ('annual', 'Annual'),
        ('quarterly', 'Quarterly'),
        ('probation', 'Probation'),
        ('project', 'Project'),
    ]
    
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    review_type = models.CharField(max_length=20, choices=REVIEW_TYPE_CHOICES, null=True, blank=True)
    criteria = models.JSONField(default=dict)  # Flexible criteria structure
    rating_scale = models.IntegerField(default=5)
    is_active = models.BooleanField(default=True)
    
    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_review_templates')
    
    class Meta:
        db_table = 'performance_review_templates'
        verbose_name = 'Performance Review Template'
        verbose_name_plural = 'Performance Review Templates'
        ordering = ['name']
    
    def __str__(self):
        return self.name


class PerformanceReview(models.Model):
    """
    Performance reviews for employees
    Based on performance_reviews table from database.txt
    """
    REVIEW_TYPE_CHOICES = [
        ('annual', 'Annual'),
        ('quarterly', 'Quarterly'),
        ('probation', 'Probation'),
        ('project', 'Project'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending_employee', 'Pending Employee'),
        ('pending_manager', 'Pending Manager'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]
    
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='performance_reviews')
    reviewer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conducted_reviews')
    template = models.ForeignKey(PerformanceReviewTemplate, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviews')
    review_period_start = models.DateField()
    review_period_end = models.DateField()
    review_type = models.CharField(max_length=20, choices=REVIEW_TYPE_CHOICES, null=True, blank=True)
    
    # Overall ratings
    overall_rating = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    technical_skills = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    communication = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    teamwork = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    leadership = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    problem_solving = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    time_management = models.DecimalField(max_digits=3, decimal_places=2, null=True, blank=True)
    
    # Comments and feedback
    strengths = models.TextField(blank=True, null=True)
    areas_for_improvement = models.TextField(blank=True, null=True)
    reviewer_comments = models.TextField(blank=True, null=True)
    employee_comments = models.TextField(blank=True, null=True)
    goals_for_next_period = models.TextField(blank=True, null=True)
    
    # Status and workflow
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    employee_acknowledged = models.BooleanField(default=False)
    employee_acknowledged_at = models.DateTimeField(null=True, blank=True)
    
    # Dates
    due_date = models.DateField(null=True, blank=True)
    completed_date = models.DateField(null=True, blank=True)
    next_review_date = models.DateField(null=True, blank=True)
    
    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'performance_reviews'
        verbose_name = 'Performance Review'
        verbose_name_plural = 'Performance Reviews'
        ordering = ['-review_period_end']
    
    def __str__(self):
        return f"{self.employee.first_name} {self.employee.last_name} - {self.review_period_start} to {self.review_period_end}"


class PerformanceGoal(models.Model):
    """
    Performance goals for employees
    Based on performance_goals table from database.txt
    """
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]
    
    STATUS_CHOICES = [
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('overdue', 'Overdue'),
    ]
    
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='performance_goals')
    review = models.ForeignKey(PerformanceReview, on_delete=models.SET_NULL, null=True, blank=True, related_name='goals')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    target_date = models.DateField(null=True, blank=True)
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_started')
    progress_percentage = models.IntegerField(default=0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    completion_date = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True, null=True)
    
    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_goals')
    
    class Meta:
        db_table = 'performance_goals'
        verbose_name = 'Performance Goal'
        verbose_name_plural = 'Performance Goals'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.employee.first_name} {self.employee.last_name} - {self.title}"
