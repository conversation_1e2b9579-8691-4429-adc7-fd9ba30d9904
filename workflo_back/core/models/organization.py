from django.db import models
from django.utils import timezone
from .auth import User


class Department(models.Model):
    """
    Departments model
    Based on departments table from database.txt
    """
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    supervisor = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='supervised_departments')
    parent_department = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='sub_departments')
    budget = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    location = models.CharField(max_length=255, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    
    # Audit fields
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.Foreign<PERSON>ey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_departments')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_departments')
    
    class Meta:
        db_table = 'departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def get_all_employees(self):
        """Get all employees in this department including sub-departments"""
        from .employees import EmployeeProfile
        employees = EmployeeProfile.objects.filter(department=self)
        
        # Add employees from sub-departments
        for sub_dept in self.sub_departments.all():
            employees = employees.union(sub_dept.get_all_employees())
        
        return employees
    
    def get_hierarchy_level(self):
        """Get the hierarchy level of this department"""
        level = 0
        current = self.parent_department
        while current:
            level += 1
            current = current.parent_department
        return level
